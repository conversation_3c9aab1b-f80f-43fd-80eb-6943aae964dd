import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Settings as SettingsIcon,
  Moon,
  Sun,
  Bell,
  Download,
  Upload,
  Trash2,
  User,
  Shield,
  Smartphone,
  Globe,
  Database,
  Palette,
  Volume2,
  VolumeX
} from 'lucide-react'
import { useAppStore } from '../stores/useStore'
import { Card } from '../components/UI/Card'
import { Button } from '../components/UI/Button'
import { Input } from '../components/UI/Input'
import toast from 'react-hot-toast'

export default function Settings() {
  const { theme, setTheme } = useAppStore()
  const [notifications, setNotifications] = useState(true)
  const [sounds, setSounds] = useState(true)
  const [autoSync, setAutoSync] = useState(true)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  }

  const handleThemeToggle = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
    document.documentElement.classList.toggle('dark', newTheme === 'dark')
    toast.success(`Switched to ${newTheme} mode`)
  }

  const handleExportData = () => {
    toast.loading('Exporting data...')
    // Simulate export
    setTimeout(() => {
      toast.success('Data exported successfully!')
    }, 2000)
  }

  const handleImportData = () => {
    toast.info('Import feature coming soon!')
  }

  const handleClearData = () => {
    toast.error('This will delete all your data. Feature coming soon.')
  }

  const settingSections = [
    {
      title: 'Appearance',
      icon: Palette,
      settings: [
        {
          label: 'Theme',
          description: 'Choose your preferred theme',
          component: (
            <Button
              variant="outline"
              onClick={handleThemeToggle}
              className="w-auto"
            >
              {theme === 'light' ? (
                <>
                  <Moon className="w-4 h-4 mr-2" />
                  Dark Mode
                </>
              ) : (
                <>
                  <Sun className="w-4 h-4 mr-2" />
                  Light Mode
                </>
              )}
            </Button>
          )
        }
      ]
    },
    {
      title: 'Notifications',
      icon: Bell,
      settings: [
        {
          label: 'Push Notifications',
          description: 'Receive notifications for important updates',
          component: (
            <Button
              variant={notifications ? "default" : "outline"}
              onClick={() => {
                setNotifications(!notifications)
                toast.success(`Notifications ${!notifications ? 'enabled' : 'disabled'}`)
              }}
              size="sm"
            >
              {notifications ? 'Enabled' : 'Disabled'}
            </Button>
          )
        },
        {
          label: 'Sound Effects',
          description: 'Play sounds for interactions',
          component: (
            <Button
              variant={sounds ? "default" : "outline"}
              onClick={() => {
                setSounds(!sounds)
                toast.success(`Sounds ${!sounds ? 'enabled' : 'disabled'}`)
              }}
              size="sm"
            >
              {sounds ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
            </Button>
          )
        }
      ]
    },
    {
      title: 'Data & Storage',
      icon: Database,
      settings: [
        {
          label: 'Auto Sync',
          description: 'Automatically sync data across devices',
          component: (
            <Button
              variant={autoSync ? "default" : "outline"}
              onClick={() => {
                setAutoSync(!autoSync)
                toast.success(`Auto sync ${!autoSync ? 'enabled' : 'disabled'}`)
              }}
              size="sm"
            >
              {autoSync ? 'On' : 'Off'}
            </Button>
          )
        },
        {
          label: 'Export Data',
          description: 'Download your data as JSON file',
          component: (
            <Button
              variant="outline"
              onClick={handleExportData}
              size="sm"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          )
        },
        {
          label: 'Import Data',
          description: 'Import data from backup file',
          component: (
            <Button
              variant="outline"
              onClick={handleImportData}
              size="sm"
            >
              <Upload className="w-4 h-4 mr-2" />
              Import
            </Button>
          )
        },
        {
          label: 'Clear All Data',
          description: 'Delete all your grocery data',
          component: (
            <Button
              variant="destructive"
              onClick={handleClearData}
              size="sm"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Clear
            </Button>
          )
        }
      ]
    },
    {
      title: 'Account',
      icon: User,
      settings: [
        {
          label: 'Profile',
          description: 'Manage your profile information',
          component: (
            <Button variant="outline" size="sm">
              <User className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
          )
        },
        {
          label: 'Privacy',
          description: 'Control your privacy settings',
          component: (
            <Button variant="outline" size="sm">
              <Shield className="w-4 h-4 mr-2" />
              Privacy
            </Button>
          )
        }
      ]
    }
  ]

  return (
    <div className="container py-8">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-4xl mx-auto"
      >
        {/* Header */}
        <motion.div variants={itemVariants} className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
              <SettingsIcon className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">Settings</h1>
              <p className="text-muted-foreground">Customize your Grocify experience</p>
            </div>
          </div>
        </motion.div>

        {/* Settings Sections */}
        <div className="space-y-6">
          {settingSections.map((section, sectionIndex) => (
            <motion.div
              key={section.title}
              variants={itemVariants}
              style={{ animationDelay: `${sectionIndex * 100}ms` }}
            >
              <Card className="p-6">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                    <section.icon className="w-4 h-4 text-primary" />
                  </div>
                  <h2 className="text-xl font-semibold text-foreground">{section.title}</h2>
                </div>

                <div className="space-y-4">
                  {section.settings.map((setting, settingIndex) => (
                    <motion.div
                      key={setting.label}
                      className="flex items-center justify-between p-4 rounded-lg border border-border/50 hover:border-border transition-colors"
                      whileHover={{ scale: 1.01 }}
                    >
                      <div className="flex-1">
                        <h3 className="font-medium text-foreground">{setting.label}</h3>
                        <p className="text-sm text-muted-foreground">{setting.description}</p>
                      </div>
                      <div className="ml-4">
                        {setting.component}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* App Info */}
        <motion.div variants={itemVariants} className="mt-8">
          <Card className="p-6 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-primary to-primary/60 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Smartphone className="w-8 h-8 text-primary-foreground" />
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Grocify PWA</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Version 1.0.0 • Built with React & Tailwind CSS
            </p>
            <div className="flex justify-center gap-4">
              <Button variant="outline" size="sm">
                <Globe className="w-4 h-4 mr-2" />
                Website
              </Button>
              <Button variant="outline" size="sm">
                Privacy Policy
              </Button>
              <Button variant="outline" size="sm">
                Terms of Service
              </Button>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </div>
  )
}
