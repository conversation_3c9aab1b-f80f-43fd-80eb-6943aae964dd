import { useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Plus, 
  Search, 
  Filter, 
  Check, 
  X, 
  Edit3, 
  Trash2,
  GripVertical,
  ShoppingCart,
  RotateCcw
} from 'lucide-react'
import { useGroceryStore } from '../stores/useStore'
import toast from 'react-hot-toast'

const categories = [
  'All',
  'Fruits & Vegetables',
  'Meat & Seafood',
  'Dairy & Eggs',
  'Bakery',
  'Pantry',
  'Frozen',
  'Beverages',
  'Snacks',
  'Other'
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.3
    }
  },
  exit: {
    opacity: 0,
    x: 20,
    transition: {
      duration: 0.2
    }
  }
}

export default function GroceryList() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [newItem, setNewItem] = useState({ name: '', quantity: 1, category: 'Other' })
  
  const { 
    items, 
    suggestions, 
    addItem, 
    updateItem, 
    deleteItem, 
    toggleItem, 
    clearCompleted 
  } = useGroceryStore()

  const inputRef = useRef(null)

  // Filter items based on search and category
  const filteredItems = items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'All' || item.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const completedItems = filteredItems.filter(item => item.completed)
  const pendingItems = filteredItems.filter(item => !item.completed)

  const handleAddItem = () => {
    if (!newItem.name.trim()) {
      toast.error('Please enter an item name')
      return
    }

    addItem(newItem)
    setNewItem({ name: '', quantity: 1, category: 'Other' })
    setShowAddForm(false)
    toast.success('Item added to list')
  }

  const handleEditItem = (item) => {
    setEditingItem(item.id)
    setNewItem({ 
      name: item.name, 
      quantity: item.quantity, 
      category: item.category 
    })
  }

  const handleUpdateItem = () => {
    if (!newItem.name.trim()) {
      toast.error('Please enter an item name')
      return
    }

    updateItem(editingItem, newItem)
    setEditingItem(null)
    setNewItem({ name: '', quantity: 1, category: 'Other' })
    toast.success('Item updated')
  }

  const handleDeleteItem = (id) => {
    deleteItem(id)
    toast.success('Item removed from list')
  }

  const handleToggleItem = (id) => {
    toggleItem(id)
  }

  const handleClearCompleted = () => {
    clearCompleted()
    toast.success('Completed items cleared')
  }

  const filteredSuggestions = suggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(newItem.name.toLowerCase()) &&
    suggestion.toLowerCase() !== newItem.name.toLowerCase()
  ).slice(0, 5)

  return (
    <motion.div 
      className="container py-8"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-secondary-900 mb-2">
              Grocery List
            </h1>
            <p className="text-secondary-600">
              {pendingItems.length} items to buy, {completedItems.length} completed
            </p>
          </div>
          
          <motion.button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center space-x-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Plus className="w-4 h-4" />
            <span>Add Item</span>
          </motion.button>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400" />
            <input
              type="text"
              placeholder="Search items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10"
            />
          </div>
          
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="input pl-10 pr-8 appearance-none bg-white"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
        </div>
      </motion.div>

      {/* Add/Edit Form */}
      <AnimatePresence>
        {(showAddForm || editingItem) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="card mb-6"
          >
            <h3 className="text-lg font-semibold text-secondary-900 mb-4">
              {editingItem ? 'Edit Item' : 'Add New Item'}
            </h3>
            
            <div className="space-y-4">
              <div className="relative">
                <input
                  ref={inputRef}
                  type="text"
                  placeholder="Item name"
                  value={newItem.name}
                  onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                  className="input"
                  autoFocus
                />
                
                {/* Suggestions */}
                {filteredSuggestions.length > 0 && newItem.name && (
                  <div className="absolute top-full left-0 right-0 bg-white border border-secondary-200 rounded-md shadow-lg z-10 mt-1">
                    {filteredSuggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => setNewItem({ ...newItem, name: suggestion })}
                        className="w-full text-left px-3 py-2 hover:bg-secondary-50 text-sm"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-1">
                    Quantity
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={newItem.quantity}
                    onChange={(e) => setNewItem({ ...newItem, quantity: parseInt(e.target.value) || 1 })}
                    className="input"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-1">
                    Category
                  </label>
                  <select
                    value={newItem.category}
                    onChange={(e) => setNewItem({ ...newItem, category: e.target.value })}
                    className="input"
                  >
                    {categories.slice(1).map(category => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <motion.button
                  onClick={editingItem ? handleUpdateItem : handleAddItem}
                  className="btn-primary"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {editingItem ? 'Update Item' : 'Add Item'}
                </motion.button>
                
                <motion.button
                  onClick={() => {
                    setShowAddForm(false)
                    setEditingItem(null)
                    setNewItem({ name: '', quantity: 1, category: 'Other' })
                  }}
                  className="btn-ghost"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Cancel
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Items List */}
      <motion.div variants={itemVariants}>
        {filteredItems.length === 0 ? (
          <div className="card text-center py-12">
            <ShoppingCart className="w-16 h-16 text-secondary-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-secondary-500 mb-2">
              {items.length === 0 ? 'Your grocery list is empty' : 'No items match your search'}
            </h3>
            <p className="text-secondary-400 mb-6">
              {items.length === 0 
                ? 'Add your first item to get started' 
                : 'Try adjusting your search or filter'
              }
            </p>
            {items.length === 0 && (
              <motion.button
                onClick={() => setShowAddForm(true)}
                className="btn-primary"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Add First Item
              </motion.button>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            {/* Pending Items */}
            {pendingItems.length > 0 && (
              <div className="card">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-secondary-900">
                    To Buy ({pendingItems.length})
                  </h3>
                </div>
                
                <AnimatePresence>
                  <div className="space-y-2">
                    {pendingItems.map((item) => (
                      <motion.div
                        key={item.id}
                        variants={itemVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        className="flex items-center space-x-3 p-3 rounded-lg hover:bg-secondary-50 group"
                      >
                        <motion.button
                          onClick={() => handleToggleItem(item.id)}
                          className="w-5 h-5 border-2 border-secondary-300 rounded hover:border-primary-500 flex items-center justify-center"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          {item.completed && (
                            <Check className="w-3 h-3 text-primary-600" />
                          )}
                        </motion.button>
                        
                        <GripVertical className="w-4 h-4 text-secondary-300 cursor-move" />
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-secondary-900">
                              {item.name}
                            </span>
                            <span className="text-sm text-secondary-500">
                              x{item.quantity}
                            </span>
                          </div>
                          <span className="text-xs text-secondary-400">
                            {item.category}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <motion.button
                            onClick={() => handleEditItem(item)}
                            className="p-1 text-secondary-400 hover:text-primary-600"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <Edit3 className="w-4 h-4" />
                          </motion.button>
                          
                          <motion.button
                            onClick={() => handleDeleteItem(item.id)}
                            className="p-1 text-secondary-400 hover:text-danger-600"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <Trash2 className="w-4 h-4" />
                          </motion.button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </AnimatePresence>
              </div>
            )}

            {/* Completed Items */}
            {completedItems.length > 0 && (
              <div className="card">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-secondary-900">
                    Completed ({completedItems.length})
                  </h3>
                  
                  <motion.button
                    onClick={handleClearCompleted}
                    className="btn-ghost text-sm flex items-center space-x-1"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <RotateCcw className="w-3 h-3" />
                    <span>Clear</span>
                  </motion.button>
                </div>
                
                <AnimatePresence>
                  <div className="space-y-2">
                    {completedItems.map((item) => (
                      <motion.div
                        key={item.id}
                        variants={itemVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        className="flex items-center space-x-3 p-3 rounded-lg hover:bg-secondary-50 group opacity-60"
                      >
                        <motion.button
                          onClick={() => handleToggleItem(item.id)}
                          className="w-5 h-5 bg-success-500 border-2 border-success-500 rounded flex items-center justify-center"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Check className="w-3 h-3 text-white" />
                        </motion.button>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-secondary-900 line-through">
                              {item.name}
                            </span>
                            <span className="text-sm text-secondary-500">
                              x{item.quantity}
                            </span>
                          </div>
                          <span className="text-xs text-secondary-400">
                            {item.category}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <motion.button
                            onClick={() => handleDeleteItem(item.id)}
                            className="p-1 text-secondary-400 hover:text-danger-600"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <Trash2 className="w-4 h-4" />
                          </motion.button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </AnimatePresence>
              </div>
            )}
          </div>
        )}
      </motion.div>
    </motion.div>
  )
}
