import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Plus, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Store,
  Calendar,
  BarChart3,
  Search,
  Filter
} from 'lucide-react'
import { usePriceStore, useGroceryStore } from '../stores/useStore'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import toast from 'react-hot-toast'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3
    }
  }
}

export default function PriceTracker() {
  const [showAddForm, setShowAddForm] = useState(false)
  const [selectedItem, setSelectedItem] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStore, setSelectedStore] = useState('All')
  const [newPrice, setNewPrice] = useState({
    itemName: '',
    price: '',
    store: 'Walmart',
    notes: ''
  })

  const { prices, stores, addPrice, getPriceHistory, getLowestPrice } = usePriceStore()
  const { suggestions } = useGroceryStore()

  // Get unique items that have been tracked
  const trackedItems = [...new Set(prices.map(p => p.itemName))]
  
  // Filter prices based on search and store
  const filteredPrices = prices.filter(price => {
    const matchesSearch = price.itemName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStore = selectedStore === 'All' || price.store === selectedStore
    return matchesSearch && matchesStore
  })

  // Group prices by item for display
  const groupedPrices = trackedItems.reduce((acc, itemName) => {
    const itemPrices = filteredPrices.filter(p => p.itemName === itemName)
    if (itemPrices.length > 0) {
      const latest = itemPrices[0]
      const lowest = getLowestPrice(itemName)
      const history = getPriceHistory(itemName)
      
      acc[itemName] = {
        latest,
        lowest,
        history: history.slice(0, 10), // Last 10 entries
        trend: history.length > 1 ? 
          (history[0].price > history[1].price ? 'up' : 
           history[0].price < history[1].price ? 'down' : 'stable') : 'stable'
      }
    }
    return acc
  }, {})

  const handleAddPrice = () => {
    if (!newPrice.itemName.trim() || !newPrice.price || !newPrice.store) {
      toast.error('Please fill in all required fields')
      return
    }

    const priceValue = parseFloat(newPrice.price)
    if (isNaN(priceValue) || priceValue <= 0) {
      toast.error('Please enter a valid price')
      return
    }

    addPrice({
      ...newPrice,
      price: priceValue
    })

    setNewPrice({
      itemName: '',
      price: '',
      store: 'Walmart',
      notes: ''
    })
    setShowAddForm(false)
    toast.success('Price added successfully')
  }

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  }

  const prepareChartData = (history) => {
    return history.slice().reverse().map(entry => ({
      date: formatDate(entry.date),
      price: entry.price,
      store: entry.store
    }))
  }

  const filteredSuggestions = [...suggestions, ...trackedItems]
    .filter(suggestion =>
      suggestion.toLowerCase().includes(newPrice.itemName.toLowerCase()) &&
      suggestion.toLowerCase() !== newPrice.itemName.toLowerCase()
    )
    .slice(0, 5)

  return (
    <motion.div 
      className="container py-8"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-secondary-900 mb-2">
              Price Tracker
            </h1>
            <p className="text-secondary-600">
              Track prices across stores and find the best deals
            </p>
          </div>
          
          <motion.button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center space-x-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Plus className="w-4 h-4" />
            <span>Add Price</span>
          </motion.button>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400" />
            <input
              type="text"
              placeholder="Search items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10"
            />
          </div>
          
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400" />
            <select
              value={selectedStore}
              onChange={(e) => setSelectedStore(e.target.value)}
              className="input pl-10 pr-8 appearance-none bg-white"
            >
              <option value="All">All Stores</option>
              {stores.map(store => (
                <option key={store} value={store}>
                  {store}
                </option>
              ))}
            </select>
          </div>
        </div>
      </motion.div>

      {/* Add Price Form */}
      <AnimatePresence>
        {showAddForm && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="card mb-6"
          >
            <h3 className="text-lg font-semibold text-secondary-900 mb-4">
              Add New Price
            </h3>
            
            <div className="space-y-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Item name"
                  value={newPrice.itemName}
                  onChange={(e) => setNewPrice({ ...newPrice, itemName: e.target.value })}
                  className="input"
                />
                
                {/* Suggestions */}
                {filteredSuggestions.length > 0 && newPrice.itemName && (
                  <div className="absolute top-full left-0 right-0 bg-white border border-secondary-200 rounded-md shadow-lg z-10 mt-1">
                    {filteredSuggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => setNewPrice({ ...newPrice, itemName: suggestion })}
                        className="w-full text-left px-3 py-2 hover:bg-secondary-50 text-sm"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400" />
                  <input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={newPrice.price}
                    onChange={(e) => setNewPrice({ ...newPrice, price: e.target.value })}
                    className="input pl-10"
                  />
                </div>
                
                <div className="relative">
                  <Store className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400" />
                  <select
                    value={newPrice.store}
                    onChange={(e) => setNewPrice({ ...newPrice, store: e.target.value })}
                    className="input pl-10 pr-8 appearance-none bg-white"
                  >
                    {stores.map(store => (
                      <option key={store} value={store}>
                        {store}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              <input
                type="text"
                placeholder="Notes (optional)"
                value={newPrice.notes}
                onChange={(e) => setNewPrice({ ...newPrice, notes: e.target.value })}
                className="input"
              />
              
              <div className="flex items-center space-x-3">
                <motion.button
                  onClick={handleAddPrice}
                  className="btn-primary"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Add Price
                </motion.button>
                
                <motion.button
                  onClick={() => {
                    setShowAddForm(false)
                    setNewPrice({
                      itemName: '',
                      price: '',
                      store: 'Walmart',
                      notes: ''
                    })
                  }}
                  className="btn-ghost"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Cancel
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Price Overview */}
      <motion.div variants={itemVariants}>
        {Object.keys(groupedPrices).length === 0 ? (
          <div className="card text-center py-12">
            <BarChart3 className="w-16 h-16 text-secondary-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-secondary-500 mb-2">
              No prices tracked yet
            </h3>
            <p className="text-secondary-400 mb-6">
              Start tracking prices to see trends and find the best deals
            </p>
            <motion.button
              onClick={() => setShowAddForm(true)}
              className="btn-primary"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Add First Price
            </motion.button>
          </div>
        ) : (
          <div className="space-y-6">
            {Object.entries(groupedPrices).map(([itemName, data]) => (
              <motion.div
                key={itemName}
                className="card"
                variants={itemVariants}
                whileHover={{ y: -2 }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-secondary-900">
                      {itemName}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-secondary-600">
                      <span className="flex items-center space-x-1">
                        <Store className="w-3 h-3" />
                        <span>{data.latest.store}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3" />
                        <span>{formatDate(data.latest.date)}</span>
                      </span>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl font-bold text-secondary-900">
                        {formatPrice(data.latest.price)}
                      </span>
                      {data.trend === 'up' && (
                        <TrendingUp className="w-5 h-5 text-danger-500" />
                      )}
                      {data.trend === 'down' && (
                        <TrendingDown className="w-5 h-5 text-success-500" />
                      )}
                    </div>
                    <div className="text-sm text-secondary-500">
                      Lowest: {formatPrice(data.lowest)}
                    </div>
                  </div>
                </div>

                {/* Price History Chart */}
                {data.history.length > 1 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-secondary-700 mb-2">
                      Price History
                    </h4>
                    <div className="h-32">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={prepareChartData(data.history)}>
                          <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                          <XAxis 
                            dataKey="date" 
                            tick={{ fontSize: 12 }}
                            stroke="#64748b"
                          />
                          <YAxis 
                            tick={{ fontSize: 12 }}
                            stroke="#64748b"
                            tickFormatter={(value) => `$${value}`}
                          />
                          <Tooltip 
                            formatter={(value) => [formatPrice(value), 'Price']}
                            labelFormatter={(label) => `Date: ${label}`}
                          />
                          <Line 
                            type="monotone" 
                            dataKey="price" 
                            stroke="#0ea5e9" 
                            strokeWidth={2}
                            dot={{ fill: '#0ea5e9', strokeWidth: 2, r: 4 }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                )}

                {/* Recent Entries */}
                <div className="mt-4 pt-4 border-t border-secondary-200">
                  <h4 className="text-sm font-medium text-secondary-700 mb-2">
                    Recent Entries
                  </h4>
                  <div className="space-y-1">
                    {data.history.slice(0, 3).map((entry, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <span className="text-secondary-600">
                          {entry.store} • {formatDate(entry.date)}
                        </span>
                        <span className="font-medium text-secondary-900">
                          {formatPrice(entry.price)}
                        </span>
                      </div>
                    ))}
                  </div>
                  
                  {data.history.length > 3 && (
                    <button
                      onClick={() => setSelectedItem(itemName)}
                      className="text-xs text-primary-600 hover:text-primary-700 mt-2"
                    >
                      View all {data.history.length} entries
                    </button>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>
    </motion.div>
  )
}
