import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Bell, X, Settings, AlertTriangle, Clock, DollarSign } from 'lucide-react'
import { useAppStore, usePantryStore, usePriceStore } from '../../stores/useStore'
import notificationService from '../../services/notificationService'
import toast from 'react-hot-toast'

export default function NotificationManager() {
  const [showPermissionPrompt, setShowPermissionPrompt] = useState(false)
  const [notificationPermission, setNotificationPermission] = useState('default')
  const { preferences } = useAppStore()
  const { getExpiringItems, getExpiredItems } = usePantryStore()
  const { prices } = usePriceStore()

  useEffect(() => {
    // Check notification permission status
    const checkPermission = () => {
      const permission = notificationService.getPermissionStatus()
      setNotificationPermission(permission)
      
      // Show permission prompt if notifications are enabled but permission not granted
      if (preferences.notifications && permission === 'default') {
        setTimeout(() => setShowPermissionPrompt(true), 2000)
      }
    }

    checkPermission()
    
    // Check for expiring items on mount and periodically
    checkExpiringItems()
    const expiryInterval = setInterval(checkExpiringItems, 60 * 60 * 1000) // Every hour

    return () => {
      clearInterval(expiryInterval)
    }
  }, [preferences.notifications])

  const checkExpiringItems = () => {
    if (!preferences.notifications) return

    const expiredItems = getExpiredItems()
    const expiringItems = getExpiringItems(3) // Items expiring in 3 days

    // Show notifications for expired items
    if (expiredItems.length > 0) {
      const title = expiredItems.length === 1 
        ? `${expiredItems[0].name} has expired!`
        : `${expiredItems.length} items have expired!`
      
      notificationService.showNotification(title, {
        body: 'Check your pantry and remove expired items',
        icon: '⚠️',
        tag: 'expired-items',
        requireInteraction: true
      })
    }

    // Show notifications for expiring items
    if (expiringItems.length > 0) {
      const title = expiringItems.length === 1 
        ? `${expiringItems[0].name} expires soon!`
        : `${expiringItems.length} items expire soon!`
      
      notificationService.showNotification(title, {
        body: 'Use these items within the next 3 days',
        icon: '⏰',
        tag: 'expiring-items'
      })
    }
  }

  const handleEnableNotifications = async () => {
    const granted = await notificationService.requestPermission()
    if (granted) {
      setNotificationPermission('granted')
      setShowPermissionPrompt(false)
      toast.success('Notifications enabled! You\'ll get alerts for expiring items.')
    } else {
      toast.error('Notifications blocked. You can enable them in your browser settings.')
    }
  }

  const handleDismissPrompt = () => {
    setShowPermissionPrompt(false)
    toast('You can enable notifications later in Settings', {
      icon: 'ℹ️',
      duration: 3000
    })
  }

  // Test notification function (for development)
  const testNotification = () => {
    notificationService.showNotification('Test Notification', {
      body: 'This is a test notification from Grocify!',
      icon: '🧪',
      tag: 'test'
    })
  }

  return (
    <>
      {/* Permission Prompt */}
      <AnimatePresence>
        {showPermissionPrompt && (
          <motion.div
            initial={{ opacity: 0, y: -100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -100 }}
            className="fixed top-20 left-4 right-4 z-50 md:left-auto md:right-4 md:w-96"
          >
            <div className="bg-white rounded-lg shadow-lg border border-secondary-200 p-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                    <Bell className="w-5 h-5 text-primary-600" />
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-secondary-900">
                    Enable Notifications
                  </h3>
                  <p className="text-xs text-secondary-600 mt-1">
                    Get alerts for expiring items, price drops, and shopping reminders.
                  </p>
                  
                  <div className="flex items-center space-x-2 mt-3">
                    <motion.button
                      onClick={handleEnableNotifications}
                      className="bg-primary-600 text-white px-3 py-1.5 rounded-md text-xs font-medium"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Enable
                    </motion.button>
                    
                    <motion.button
                      onClick={handleDismissPrompt}
                      className="text-xs text-secondary-500 hover:text-secondary-700 px-2 py-1.5"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Not now
                    </motion.button>
                  </div>
                </div>
                
                <motion.button
                  onClick={handleDismissPrompt}
                  className="flex-shrink-0 p-1 text-secondary-400 hover:text-secondary-600"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <X className="w-4 h-4" />
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Notification Status Indicator (for development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 z-50">
          <div className="bg-white rounded-lg shadow-lg border border-secondary-200 p-3 text-xs">
            <div className="flex items-center space-x-2 mb-2">
              <Bell className="w-3 h-3" />
              <span className="font-medium">Notifications</span>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span>Permission:</span>
                <span className={`font-medium ${
                  notificationPermission === 'granted' ? 'text-success-600' :
                  notificationPermission === 'denied' ? 'text-danger-600' :
                  'text-warning-600'
                }`}>
                  {notificationPermission}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Enabled:</span>
                <span className={`font-medium ${preferences.notifications ? 'text-success-600' : 'text-secondary-500'}`}>
                  {preferences.notifications ? 'Yes' : 'No'}
                </span>
              </div>
              <button
                onClick={testNotification}
                className="w-full mt-2 bg-primary-600 text-white px-2 py-1 rounded text-xs"
              >
                Test
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

// Hook for using notifications in components
export function useNotifications() {
  const { preferences } = useAppStore()

  const showExpiryAlert = (items) => {
    if (!preferences.notifications) return
    notificationService.showExpiryAlert(items)
  }

  const showPriceDropAlert = (item, oldPrice, newPrice, store) => {
    if (!preferences.notifications) return
    notificationService.showPriceDropAlert(item, oldPrice, newPrice, store)
  }

  const showShoppingReminder = (itemCount) => {
    if (!preferences.notifications) return
    notificationService.showShoppingReminder(itemCount)
  }

  const showToast = (message, type = 'default', options = {}) => {
    return notificationService.showToast(message, type, options)
  }

  return {
    showExpiryAlert,
    showPriceDropAlert,
    showShoppingReminder,
    showToast,
    isEnabled: preferences.notifications,
    permission: notificationService.getPermissionStatus()
  }
}
