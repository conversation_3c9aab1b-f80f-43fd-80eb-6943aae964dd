@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  background-color: #f8fafc;
  color: #1e293b;
}

html {
  scroll-behavior: smooth;
}

/* Component styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  outline: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem 1rem;
}

.btn:focus-visible {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  pointer-events: none;
}

.btn-primary {
  background-color: #0284c7;
  color: white;
}

.btn-primary:hover {
  background-color: #0369a1;
}

.btn-secondary {
  background-color: #f1f5f9;
  color: #1e293b;
}

.btn-secondary:hover {
  background-color: #e2e8f0;
}

.btn-ghost {
  background-color: transparent;
  color: #64748b;
}

.btn-ghost:hover {
  background-color: #f1f5f9;
  color: #1e293b;
}

.card {
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  background-color: white;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.input {
  display: flex;
  height: 2.5rem;
  width: 100%;
  border-radius: 0.375rem;
  border: 1px solid #cbd5e1;
  background-color: white;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  outline: none;
}

.input:focus {
  outline: 2px solid #0ea5e9;
  outline-offset: 2px;
}

.input::placeholder {
  color: #64748b;
}

.container {
  margin-left: auto;
  margin-right: auto;
  max-width: 80rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Utility classes */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.safe-top {
  padding-top: env(safe-area-inset-top);
}

.safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
