@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* CSS Variables for Dark Mode Support */
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;

    /* Custom color variables */
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --info: 199 89% 48%;
    --info-foreground: 210 40% 98%;

    /* Shadow variables */
    --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
    --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-large: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
    --shadow-glow-lg: 0 0 40px rgba(59, 130, 246, 0.4);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;

    /* Dark mode shadows */
    --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.3), 0 10px 20px -2px rgba(0, 0, 0, 0.2);
    --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
    --shadow-large: 0 10px 40px -10px rgba(0, 0, 0, 0.5), 0 20px 25px -5px rgba(0, 0, 0, 0.3);
    --shadow-glow: 0 0 20px rgba(96, 165, 250, 0.4);
    --shadow-glow-lg: 0 0 40px rgba(96, 165, 250, 0.5);
  }

  /* Base element styling */
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  html {
    scroll-behavior: smooth;
    scroll-padding-top: 4rem;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-secondary/20;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-secondary/40 rounded-full;
    transition: background-color 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary/60;
  }

  /* Selection styling */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

@layer components {
  /* Enhanced Button Components */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden;
    box-shadow: var(--shadow-soft);
  }

  .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
  }

  .btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-soft);
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground;
  }

  .btn-primary:hover {
    @apply bg-primary/90;
    box-shadow: var(--shadow-glow);
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground border border-input;
  }

  .btn-secondary:hover {
    @apply bg-secondary/80;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
    box-shadow: none;
  }

  .btn-ghost:hover {
    transform: none;
    box-shadow: var(--shadow-soft);
  }

  .btn-outline {
    @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }

  .btn-destructive {
    @apply bg-destructive text-destructive-foreground;
  }

  .btn-destructive:hover {
    @apply bg-destructive/90;
  }

  /* Button sizes */
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-12 px-8 text-base;
  }

  .btn-icon {
    @apply h-10 w-10 p-0;
  }

  /* Loading button state */
  .btn-loading {
    @apply pointer-events-none;
  }

  .btn-loading::before {
    content: '';
    @apply absolute inset-0 bg-current opacity-20 animate-pulse;
  }

  /* Enhanced Card Components */
  .card {
    @apply rounded-xl border bg-card text-card-foreground transition-all duration-300;
    box-shadow: var(--shadow-soft);
  }

  .card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
  }

  .card-interactive {
    @apply cursor-pointer;
  }

  .card-interactive:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-large);
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Glass morphism cards */
  .card-glass {
    @apply backdrop-blur-md border border-white/20;
    background: rgba(255, 255, 255, 0.1);
  }

  .dark .card-glass {
    background: rgba(0, 0, 0, 0.2);
    @apply border-white/10;
  }

  /* Enhanced Input Components */
  .input {
    @apply flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  .input:focus {
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
  }

  .input-error {
    @apply border-destructive focus-visible:ring-destructive;
  }

  .input-success {
    @apply border-green-500 focus-visible:ring-green-500;
  }

  /* Floating label inputs */
  .input-floating {
    @apply relative;
  }

  .input-floating input {
    @apply peer placeholder-transparent pt-6 pb-2;
  }

  .input-floating label {
    @apply absolute left-3 top-2 text-xs text-muted-foreground transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-sm peer-placeholder-shown:text-muted-foreground peer-focus:top-2 peer-focus:text-xs peer-focus:text-primary;
  }

  /* Container */
  .container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  /* Loading States */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  .skeleton-text {
    @apply skeleton h-4 w-full;
  }

  .skeleton-text-sm {
    @apply skeleton h-3 w-3/4;
  }

  .skeleton-avatar {
    @apply skeleton h-10 w-10 rounded-full;
  }

  .skeleton-button {
    @apply skeleton h-10 w-24;
  }

  /* Shimmer effect */
  .shimmer {
    @apply relative overflow-hidden bg-muted;
  }

  .shimmer::after {
    @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent content-[''];
    animation: shimmer 2s linear infinite;
  }

  /* Loading spinner */
  .spinner {
    @apply inline-block animate-spin rounded-full border-2 border-solid border-current border-r-transparent;
  }

  .spinner-sm {
    @apply h-4 w-4;
  }

  .spinner-md {
    @apply h-6 w-6;
  }

  .spinner-lg {
    @apply h-8 w-8;
  }

  /* Glass Effect */
  .glass {
    @apply backdrop-blur-md border border-white/20;
    background: rgba(255, 255, 255, 0.1);
  }

  .dark .glass {
    background: rgba(0, 0, 0, 0.2);
    @apply border-white/10;
  }

  /* Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent;
  }

  .gradient-text-rainbow {
    @apply bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-blue-500 to-purple-500 bg-clip-text text-transparent;
  }

  /* Modal Components */
  .modal-overlay {
    @apply fixed inset-0 z-50 bg-black/50 backdrop-blur-sm;
  }

  .modal-content {
    @apply relative z-50 w-full bg-background border rounded-xl;
    box-shadow: var(--shadow-large);
  }

  /* Toast Notifications */
  .toast {
    @apply relative flex items-start gap-3 p-4 rounded-lg border backdrop-blur-sm;
    box-shadow: var(--shadow-medium);
  }

  .toast-success {
    @apply bg-green-50 border-green-200 text-green-800;
  }

  .dark .toast-success {
    @apply bg-green-900/20 border-green-800 text-green-200;
  }

  .toast-error {
    @apply bg-red-50 border-red-200 text-red-800;
  }

  .dark .toast-error {
    @apply bg-red-900/20 border-red-800 text-red-200;
  }

  .toast-warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-800;
  }

  .dark .toast-warning {
    @apply bg-yellow-900/20 border-yellow-800 text-yellow-200;
  }

  .toast-info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
  }

  .dark .toast-info {
    @apply bg-blue-900/20 border-blue-800 text-blue-200;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-default {
    @apply border-transparent bg-primary text-primary-foreground;
  }

  .badge-secondary {
    @apply border-transparent bg-secondary text-secondary-foreground;
  }

  .badge-destructive {
    @apply border-transparent bg-destructive text-destructive-foreground;
  }

  .badge-success {
    @apply border-transparent bg-green-500 text-white;
  }

  .badge-warning {
    @apply border-transparent bg-yellow-500 text-white;
  }

  .badge-outline {
    @apply text-foreground border-current;
  }

  /* Progress Components */
  .progress {
    @apply relative h-2 w-full overflow-hidden rounded-full bg-secondary;
  }

  .progress-indicator {
    @apply h-full w-full flex-1 bg-primary transition-all duration-500 ease-out;
    background: linear-gradient(90deg, var(--primary), var(--primary-foreground));
  }

  .progress-striped .progress-indicator {
    background-image: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.15) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.15) 75%,
      transparent 75%,
      transparent
    );
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
  }

  /* Separator */
  .separator {
    @apply shrink-0 bg-border;
  }

  .separator-horizontal {
    @apply h-[1px] w-full;
  }

  .separator-vertical {
    @apply h-full w-[1px];
  }

  /* Navigation */
  .nav-link {
    @apply relative px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200;
  }

  .nav-link:hover {
    @apply bg-accent text-accent-foreground;
    transform: translateY(-1px);
  }

  .nav-link.active {
    @apply bg-primary/10 text-primary;
  }

  .nav-link.active::after {
    content: '';
    @apply absolute bottom-0 left-1/2 w-1/2 h-0.5 bg-primary rounded-full;
    transform: translateX(-50%);
  }
}

@layer utilities {
  /* Custom Animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-fade-out {
    animation: fadeOut 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-slide-left {
    animation: slideLeft 0.3s ease-out;
  }

  .animate-slide-right {
    animation: slideRight 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-scale-out {
    animation: scaleOut 0.2s ease-in;
  }

  .animate-bounce-gentle {
    animation: bounceGentle 0.6s ease-out;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }

  /* Animation delays */
  .animate-delay-75 {
    animation-delay: 75ms;
  }

  .animate-delay-100 {
    animation-delay: 100ms;
  }

  .animate-delay-150 {
    animation-delay: 150ms;
  }

  .animate-delay-200 {
    animation-delay: 200ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }

  .animate-delay-500 {
    animation-delay: 500ms;
  }

  .animate-delay-700 {
    animation-delay: 700ms;
  }

  .animate-delay-1000 {
    animation-delay: 1000ms;
  }

  /* Interactive utilities */
  .interactive {
    @apply transition-all duration-200 cursor-pointer;
  }

  .interactive:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
  }

  .interactive:active {
    transform: translateY(0);
    box-shadow: var(--shadow-soft);
  }

  .interactive-soft {
    @apply transition-all duration-200 cursor-pointer;
  }

  .interactive-soft:hover {
    transform: translateY(-1px) scale(1.02);
  }

  .interactive-soft:active {
    transform: translateY(0) scale(1);
  }

  .interactive-scale {
    @apply transition-transform duration-200 cursor-pointer;
  }

  .interactive-scale:hover {
    transform: scale(1.05);
  }

  .interactive-scale:active {
    transform: scale(0.95);
  }

  /* Utility classes */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  /* Shadow utilities */
  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }

  .shadow-large {
    box-shadow: var(--shadow-large);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .shadow-glow-lg {
    box-shadow: var(--shadow-glow-lg);
  }

  /* Backdrop utilities */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  /* Focus utilities */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }

  .focus-ring-primary {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2;
  }

  /* Transition utilities */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-bounce {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  /* Layout utilities */
  .center {
    @apply flex items-center justify-center;
  }

  .center-x {
    @apply flex justify-center;
  }

  .center-y {
    @apply flex items-center;
  }

  /* Aspect ratio utilities */
  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  .aspect-video {
    aspect-ratio: 16 / 9;
  }

  .aspect-photo {
    aspect-ratio: 4 / 3;
  }
}

/* Custom Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideLeft {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideRight {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scaleOut {
  from {
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform: scale(0.9);
    opacity: 0;
  }
}

@keyframes bounceGentle {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

@keyframes progress-bar-stripes {
  0% {
    background-position: 1rem 0;
  }
  100% {
    background-position: 0 0;
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

/* Responsive Design Enhancements */
@media (max-width: 640px) {
  .container {
    @apply px-3;
  }

  .card {
    @apply rounded-lg;
  }

  .btn-lg {
    @apply h-11 px-6 text-sm;
  }
}

@media (max-width: 480px) {
  .container {
    @apply px-2;
  }

  .card {
    @apply rounded-md p-4;
  }

  .btn {
    @apply text-xs;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .card {
    @apply shadow-none border border-gray-300;
  }

  .btn {
    @apply shadow-none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    @apply border-2;
  }

  .btn {
    @apply border-2 border-current;
  }

  .input {
    @apply border-2;
  }
}
