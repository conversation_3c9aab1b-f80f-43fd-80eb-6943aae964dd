@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    scroll-behavior: smooth;
    scroll-padding-top: 4rem;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-secondary/20;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-secondary/40 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary/60;
  }

  /* Selection */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
}

@layer components {
  /* Enhanced Button Components */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 shadow-md hover:shadow-lg active:scale-95;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-input shadow-sm hover:shadow-md active:scale-95;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground active:scale-95;
  }

  .btn-outline {
    @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground shadow-sm hover:shadow-md active:scale-95;
  }

  .btn-destructive {
    @apply bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-md hover:shadow-lg active:scale-95;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-12 px-8 text-base;
  }

  /* Enhanced Card Components */
  .card {
    @apply rounded-xl border bg-card text-card-foreground shadow-soft hover:shadow-medium transition-all duration-300;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }

  /* Enhanced Input Components */
  .input {
    @apply flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  .input-floating {
    @apply relative;
  }

  .input-floating input {
    @apply peer placeholder-transparent;
  }

  .input-floating label {
    @apply absolute left-3 -top-2.5 bg-background px-1 text-sm text-muted-foreground transition-all peer-placeholder-shown:top-2.5 peer-placeholder-shown:text-base peer-placeholder-shown:text-muted-foreground peer-focus:-top-2.5 peer-focus:text-sm peer-focus:text-primary;
  }

  /* Container */
  .container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }

  /* Loading States */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  .shimmer {
    @apply relative overflow-hidden bg-muted;
  }

  .shimmer::after {
    @apply absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-transparent via-white/20 to-transparent content-[''];
  }

  /* Glass Effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-white/10;
  }

  /* Gradient Text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent;
  }

  /* Floating Action Button */
  .fab {
    @apply fixed bottom-6 right-6 z-50 h-14 w-14 rounded-full bg-primary text-primary-foreground shadow-large hover:shadow-glow-lg transition-all duration-300 hover:scale-110 active:scale-95;
  }

  /* Modal Overlay */
  .modal-overlay {
    @apply fixed inset-0 z-50 bg-black/50 backdrop-blur-sm;
  }

  /* Tooltip */
  .tooltip {
    @apply absolute z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-xs text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95;
  }

  /* Badge */
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-default {
    @apply border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
  }

  .badge-secondary {
    @apply border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .badge-destructive {
    @apply border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80;
  }

  .badge-outline {
    @apply text-foreground;
  }

  /* Progress */
  .progress {
    @apply relative h-2 w-full overflow-hidden rounded-full bg-secondary;
  }

  .progress-indicator {
    @apply h-full w-full flex-1 bg-primary transition-all duration-300;
  }

  /* Separator */
  .separator {
    @apply shrink-0 bg-border;
  }

  .separator-horizontal {
    @apply h-[1px] w-full;
  }

  .separator-vertical {
    @apply h-full w-[1px];
  }
}

@layer utilities {
  /* Animation utilities */
  .animate-in {
    animation-duration: 150ms;
    animation-fill-mode: both;
  }

  .animate-out {
    animation-duration: 150ms;
    animation-fill-mode: both;
  }

  .fade-in-0 {
    animation-name: fadeIn;
  }

  .fade-out-0 {
    animation-name: fadeOut;
  }

  .zoom-in-95 {
    animation-name: zoomIn95;
  }

  .zoom-out-95 {
    animation-name: zoomOut95;
  }

  .slide-in-from-top-2 {
    animation-name: slideInFromTop2;
  }

  .slide-in-from-bottom-2 {
    animation-name: slideInFromBottom2;
  }

  .slide-in-from-left-2 {
    animation-name: slideInFromLeft2;
  }

  .slide-in-from-right-2 {
    animation-name: slideInFromRight2;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }

  @keyframes zoomIn95 {
    from { transform: scale(0.95); }
    to { transform: scale(1); }
  }

  @keyframes zoomOut95 {
    from { transform: scale(1); }
    to { transform: scale(0.95); }
  }

  @keyframes slideInFromTop2 {
    from { transform: translateY(-8px); }
    to { transform: translateY(0); }
  }

  @keyframes slideInFromBottom2 {
    from { transform: translateY(8px); }
    to { transform: translateY(0); }
  }

  @keyframes slideInFromLeft2 {
    from { transform: translateX(-8px); }
    to { transform: translateX(0); }
  }

  @keyframes slideInFromRight2 {
    from { transform: translateX(8px); }
    to { transform: translateX(0); }
  }

  /* Utility classes */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Interactive utilities */
  .interactive {
    @apply transition-all duration-200 hover:scale-105 active:scale-95;
  }

  .interactive-soft {
    @apply transition-all duration-200 hover:scale-[1.02] active:scale-[0.98];
  }

  /* Glow effects */
  .glow-primary {
    @apply shadow-glow;
  }

  .glow-primary-lg {
    @apply shadow-glow-lg;
  }

  /* Backdrop effects */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
}
