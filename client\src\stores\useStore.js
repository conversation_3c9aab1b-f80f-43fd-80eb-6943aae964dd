import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

// Main app store
export const useAppStore = create(
  persist(
    (set, get) => ({
      // Theme
      theme: 'light',
      setTheme: (theme) => set({ theme }),
      
      // User preferences
      preferences: {
        currency: 'USD',
        notifications: true,
        autoSuggest: true,
      },
      setPreferences: (preferences) => set({ preferences }),
      
      // PWA install prompt
      deferredPrompt: null,
      setDeferredPrompt: (prompt) => set({ deferredPrompt: prompt }),
      
      // App state
      isOnline: navigator.onLine,
      setIsOnline: (isOnline) => set({ isOnline }),
      
      // Navigation
      currentPage: 'dashboard',
      setCurrentPage: (page) => set({ currentPage: page }),
    }),
    {
      name: 'grocify-app-store',
      partialize: (state) => ({
        theme: state.theme,
        preferences: state.preferences,
        currentPage: state.currentPage,
      }),
    }
  )
)

// Sample data
const sampleGroceryItems = [
  { id: '1', name: 'Milk', category: 'Dairy', quantity: 2, unit: 'liters', completed: false, priority: 'medium', addedAt: new Date().toISOString() },
  { id: '2', name: 'Bread', category: 'Bakery', quantity: 1, unit: 'loaf', completed: true, priority: 'high', addedAt: new Date().toISOString() },
  { id: '3', name: 'Apples', category: 'Fruits', quantity: 6, unit: 'pieces', completed: false, priority: 'low', addedAt: new Date().toISOString() },
  { id: '4', name: 'Chicken Breast', category: 'Meat', quantity: 1, unit: 'kg', completed: false, priority: 'high', addedAt: new Date().toISOString() },
  { id: '5', name: 'Rice', category: 'Grains', quantity: 2, unit: 'kg', completed: false, priority: 'medium', addedAt: new Date().toISOString() },
]

// Grocery list store
export const useGroceryStore = create(
  persist(
    (set, get) => ({
      items: sampleGroceryItems,
      suggestions: ['Milk', 'Bread', 'Eggs', 'Butter', 'Cheese', 'Yogurt', 'Apples', 'Bananas', 'Chicken', 'Rice'],
      categories: ['Dairy', 'Bakery', 'Fruits', 'Vegetables', 'Meat', 'Grains', 'Canned Goods', 'Oils', 'Snacks', 'Beverages'],
      
      addItem: (item) => {
        const newItem = {
          id: Date.now().toString(),
          name: item.name,
          quantity: item.quantity || 1,
          category: item.category || 'Other',
          completed: false,
          addedAt: new Date().toISOString(),
          ...item,
        }
        set((state) => ({ 
          items: [...state.items, newItem],
          suggestions: [...new Set([...state.suggestions, item.name])]
        }))
      },
      
      updateItem: (id, updates) => {
        set((state) => ({
          items: state.items.map((item) =>
            item.id === id ? { ...item, ...updates } : item
          ),
        }))
      },
      
      deleteItem: (id) => {
        set((state) => ({
          items: state.items.filter((item) => item.id !== id),
        }))
      },
      
      toggleItem: (id) => {
        set((state) => ({
          items: state.items.map((item) =>
            item.id === id ? { ...item, completed: !item.completed } : item
          ),
        }))
      },
      
      clearCompleted: () => {
        set((state) => ({
          items: state.items.filter((item) => !item.completed),
        }))
      },
      
      reorderItems: (startIndex, endIndex) => {
        set((state) => {
          const result = Array.from(state.items)
          const [removed] = result.splice(startIndex, 1)
          result.splice(endIndex, 0, removed)
          return { items: result }
        })
      },
    }),
    {
      name: 'grocify-grocery-store',
    }
  )
)

// Price tracker store
export const usePriceStore = create(
  persist(
    (set, get) => ({
      prices: [],
      stores: ['Walmart', 'Target', 'Kroger', 'Safeway'],
      
      addPrice: (priceData) => {
        const newPrice = {
          id: Date.now().toString(),
          itemName: priceData.itemName,
          price: priceData.price,
          store: priceData.store,
          date: new Date().toISOString(),
          ...priceData,
        }
        set((state) => ({ prices: [...state.prices, newPrice] }))
      },
      
      getPriceHistory: (itemName) => {
        const { prices } = get()
        return prices
          .filter((price) => price.itemName.toLowerCase() === itemName.toLowerCase())
          .sort((a, b) => new Date(b.date) - new Date(a.date))
      },
      
      getLowestPrice: (itemName) => {
        const history = get().getPriceHistory(itemName)
        return history.length > 0 ? Math.min(...history.map(p => p.price)) : null
      },
    }),
    {
      name: 'grocify-price-store',
    }
  )
)

// Pantry store
export const usePantryStore = create(
  persist(
    (set, get) => ({
      items: [],
      
      addItem: (item) => {
        const newItem = {
          id: Date.now().toString(),
          name: item.name,
          quantity: item.quantity || 1,
          unit: item.unit || 'pcs',
          expiryDate: item.expiryDate,
          category: item.category || 'Other',
          addedAt: new Date().toISOString(),
          ...item,
        }
        set((state) => ({ items: [...state.items, newItem] }))
      },
      
      updateItem: (id, updates) => {
        set((state) => ({
          items: state.items.map((item) =>
            item.id === id ? { ...item, ...updates } : item
          ),
        }))
      },
      
      deleteItem: (id) => {
        set((state) => ({
          items: state.items.filter((item) => item.id !== id),
        }))
      },
      
      getExpiringItems: (days = 7) => {
        const { items } = get()
        const cutoffDate = new Date()
        cutoffDate.setDate(cutoffDate.getDate() + days)
        
        return items.filter((item) => {
          if (!item.expiryDate) return false
          return new Date(item.expiryDate) <= cutoffDate
        })
      },
      
      getExpiredItems: () => {
        const { items } = get()
        const today = new Date()
        
        return items.filter((item) => {
          if (!item.expiryDate) return false
          return new Date(item.expiryDate) < today
        })
      },
    }),
    {
      name: 'grocify-pantry-store',
    }
  )
)
