import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Plus, 
  Package, 
  Calendar,
  AlertTriangle,
  Clock,
  CheckCircle,
  Search,
  Filter,
  Edit3,
  Trash2
} from 'lucide-react'
import { usePantryStore } from '../stores/useStore'
import toast from 'react-hot-toast'

const categories = [
  'All',
  'Fruits & Vegetables',
  'Meat & Seafood',
  'Dairy & Eggs',
  'Pantry Staples',
  'Frozen',
  'Beverages',
  'Snacks',
  'Other'
]

const units = ['pcs', 'lbs', 'oz', 'kg', 'g', 'ml', 'l', 'cups', 'tbsp', 'tsp']

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3
    }
  }
}

export default function Pantry() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [newItem, setNewItem] = useState({
    name: '',
    quantity: 1,
    unit: 'pcs',
    category: 'Other',
    expiryDate: ''
  })

  const { 
    items, 
    addItem, 
    updateItem, 
    deleteItem, 
    getExpiringItems, 
    getExpiredItems 
  } = usePantryStore()

  // Filter items
  const filteredItems = items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'All' || item.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const expiredItems = getExpiredItems()
  const expiringItems = getExpiringItems(7)

  const getExpiryStatus = (expiryDate) => {
    if (!expiryDate) return 'none'
    
    const today = new Date()
    const expiry = new Date(expiryDate)
    const diffTime = expiry - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 0) return 'expired'
    if (diffDays <= 3) return 'critical'
    if (diffDays <= 7) return 'warning'
    return 'good'
  }

  const getExpiryColor = (status) => {
    switch (status) {
      case 'expired': return 'bg-danger-100 border-danger-300 text-danger-800'
      case 'critical': return 'bg-warning-100 border-warning-300 text-warning-800'
      case 'warning': return 'bg-yellow-100 border-yellow-300 text-yellow-800'
      case 'good': return 'bg-success-100 border-success-300 text-success-800'
      default: return 'bg-secondary-100 border-secondary-300 text-secondary-800'
    }
  }

  const getExpiryIcon = (status) => {
    switch (status) {
      case 'expired': return AlertTriangle
      case 'critical': return AlertTriangle
      case 'warning': return Clock
      case 'good': return CheckCircle
      default: return Package
    }
  }

  const handleAddItem = () => {
    if (!newItem.name.trim()) {
      toast.error('Please enter an item name')
      return
    }

    addItem(newItem)
    setNewItem({
      name: '',
      quantity: 1,
      unit: 'pcs',
      category: 'Other',
      expiryDate: ''
    })
    setShowAddForm(false)
    toast.success('Item added to pantry')
  }

  const handleEditItem = (item) => {
    setEditingItem(item.id)
    setNewItem({
      name: item.name,
      quantity: item.quantity,
      unit: item.unit,
      category: item.category,
      expiryDate: item.expiryDate || ''
    })
  }

  const handleUpdateItem = () => {
    if (!newItem.name.trim()) {
      toast.error('Please enter an item name')
      return
    }

    updateItem(editingItem, newItem)
    setEditingItem(null)
    setNewItem({
      name: '',
      quantity: 1,
      unit: 'pcs',
      category: 'Other',
      expiryDate: ''
    })
    toast.success('Item updated')
  }

  const handleDeleteItem = (id) => {
    deleteItem(id)
    toast.success('Item removed from pantry')
  }

  const formatExpiryDate = (dateString) => {
    if (!dateString) return 'No expiry date'
    
    const date = new Date(dateString)
    const today = new Date()
    const diffTime = date - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 0) {
      return `Expired ${Math.abs(diffDays)} day${Math.abs(diffDays) !== 1 ? 's' : ''} ago`
    } else if (diffDays === 0) {
      return 'Expires today'
    } else if (diffDays === 1) {
      return 'Expires tomorrow'
    } else {
      return `Expires in ${diffDays} days`
    }
  }

  return (
    <motion.div 
      className="container py-8"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-secondary-900 mb-2">
              Pantry Inventory
            </h1>
            <p className="text-secondary-600">
              {items.length} items • {expiredItems.length} expired • {expiringItems.length} expiring soon
            </p>
          </div>
          
          <motion.button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center space-x-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Plus className="w-4 h-4" />
            <span>Add Item</span>
          </motion.button>
        </div>

        {/* Alerts */}
        {(expiredItems.length > 0 || expiringItems.length > 0) && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {expiredItems.length > 0 && (
              <motion.div 
                className="bg-danger-50 border border-danger-200 rounded-lg p-4"
                whileHover={{ scale: 1.01 }}
              >
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-5 h-5 text-danger-600" />
                  <div>
                    <h3 className="font-medium text-danger-900">
                      {expiredItems.length} item{expiredItems.length > 1 ? 's' : ''} expired
                    </h3>
                    <p className="text-sm text-danger-700">
                      Remove or use immediately
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
            
            {expiringItems.length > 0 && (
              <motion.div 
                className="bg-warning-50 border border-warning-200 rounded-lg p-4"
                whileHover={{ scale: 1.01 }}
              >
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-warning-600" />
                  <div>
                    <h3 className="font-medium text-warning-900">
                      {expiringItems.length} item{expiringItems.length > 1 ? 's' : ''} expiring soon
                    </h3>
                    <p className="text-sm text-warning-700">
                      Use within 7 days
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        )}

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400" />
            <input
              type="text"
              placeholder="Search pantry items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10"
            />
          </div>
          
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="input pl-10 pr-8 appearance-none bg-white"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
        </div>
      </motion.div>

      {/* Add/Edit Form */}
      <AnimatePresence>
        {(showAddForm || editingItem) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="card mb-6"
          >
            <h3 className="text-lg font-semibold text-secondary-900 mb-4">
              {editingItem ? 'Edit Item' : 'Add New Item'}
            </h3>
            
            <div className="space-y-4">
              <input
                type="text"
                placeholder="Item name"
                value={newItem.name}
                onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                className="input"
                autoFocus
              />
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-1">
                    Quantity
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.1"
                    value={newItem.quantity}
                    onChange={(e) => setNewItem({ ...newItem, quantity: parseFloat(e.target.value) || 0 })}
                    className="input"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-1">
                    Unit
                  </label>
                  <select
                    value={newItem.unit}
                    onChange={(e) => setNewItem({ ...newItem, unit: e.target.value })}
                    className="input"
                  >
                    {units.map(unit => (
                      <option key={unit} value={unit}>
                        {unit}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-1">
                    Category
                  </label>
                  <select
                    value={newItem.category}
                    onChange={(e) => setNewItem({ ...newItem, category: e.target.value })}
                    className="input"
                  >
                    {categories.slice(1).map(category => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-secondary-700 mb-1">
                    Expiry Date
                  </label>
                  <input
                    type="date"
                    value={newItem.expiryDate}
                    onChange={(e) => setNewItem({ ...newItem, expiryDate: e.target.value })}
                    className="input"
                  />
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <motion.button
                  onClick={editingItem ? handleUpdateItem : handleAddItem}
                  className="btn-primary"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {editingItem ? 'Update Item' : 'Add Item'}
                </motion.button>
                
                <motion.button
                  onClick={() => {
                    setShowAddForm(false)
                    setEditingItem(null)
                    setNewItem({
                      name: '',
                      quantity: 1,
                      unit: 'pcs',
                      category: 'Other',
                      expiryDate: ''
                    })
                  }}
                  className="btn-ghost"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Cancel
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Items List */}
      <motion.div variants={itemVariants}>
        {filteredItems.length === 0 ? (
          <div className="card text-center py-12">
            <Package className="w-16 h-16 text-secondary-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-secondary-500 mb-2">
              {items.length === 0 ? 'Your pantry is empty' : 'No items match your search'}
            </h3>
            <p className="text-secondary-400 mb-6">
              {items.length === 0 
                ? 'Add your first item to start tracking your pantry' 
                : 'Try adjusting your search or filter'
              }
            </p>
            {items.length === 0 && (
              <motion.button
                onClick={() => setShowAddForm(true)}
                className="btn-primary"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Add First Item
              </motion.button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <AnimatePresence>
              {filteredItems.map((item) => {
                const expiryStatus = getExpiryStatus(item.expiryDate)
                const ExpiryIcon = getExpiryIcon(expiryStatus)
                
                return (
                  <motion.div
                    key={item.id}
                    variants={itemVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    className={`card hover:shadow-md transition-shadow duration-300 ${getExpiryColor(expiryStatus)} border-2`}
                    whileHover={{ y: -2 }}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="font-semibold text-secondary-900 mb-1">
                          {item.name}
                        </h3>
                        <p className="text-sm text-secondary-600">
                          {item.quantity} {item.unit} • {item.category}
                        </p>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <motion.button
                          onClick={() => handleEditItem(item)}
                          className="p-1 text-secondary-400 hover:text-primary-600"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Edit3 className="w-4 h-4" />
                        </motion.button>
                        
                        <motion.button
                          onClick={() => handleDeleteItem(item.id)}
                          className="p-1 text-secondary-400 hover:text-danger-600"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Trash2 className="w-4 h-4" />
                        </motion.button>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <ExpiryIcon className="w-4 h-4" />
                      <span className="text-sm font-medium">
                        {formatExpiryDate(item.expiryDate)}
                      </span>
                    </div>
                    
                    {item.expiryDate && (
                      <div className="text-xs text-secondary-500 mt-1">
                        {new Date(item.expiryDate).toLocaleDateString()}
                      </div>
                    )}
                  </motion.div>
                )
              })}
            </AnimatePresence>
          </div>
        )}
      </motion.div>
    </motion.div>
  )
}
