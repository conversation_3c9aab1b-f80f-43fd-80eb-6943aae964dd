import toast from 'react-hot-toast'

class NotificationService {
  constructor() {
    this.permission = 'default'
    this.init()
  }

  async init() {
    // Check if browser supports notifications
    if ('Notification' in window) {
      this.permission = Notification.permission
      
      // Request permission if not already granted
      if (this.permission === 'default') {
        this.permission = await Notification.requestPermission()
      }
    }

    // Register service worker for background notifications
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.ready
        console.log('Service Worker ready for notifications')
      } catch (error) {
        console.error('Service Worker registration failed:', error)
      }
    }
  }

  // Request notification permission
  async requestPermission() {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      this.permission = permission
      
      if (permission === 'granted') {
        toast.success('Notifications enabled!')
        return true
      } else if (permission === 'denied') {
        toast.error('Notifications blocked. Enable in browser settings.')
        return false
      }
    }
    return false
  }

  // Show browser notification
  showNotification(title, options = {}) {
    if (this.permission !== 'granted') {
      // Fallback to toast notification
      toast(title, {
        icon: options.icon || '🔔',
        duration: options.duration || 4000
      })
      return
    }

    const defaultOptions = {
      icon: '/pwa-192x192.png',
      badge: '/pwa-192x192.png',
      tag: 'grocify-notification',
      requireInteraction: false,
      silent: false,
      ...options
    }

    try {
      const notification = new Notification(title, defaultOptions)
      
      // Auto close after duration
      if (options.duration) {
        setTimeout(() => {
          notification.close()
        }, options.duration)
      }

      // Handle click events
      notification.onclick = () => {
        window.focus()
        notification.close()
        if (options.onClick) {
          options.onClick()
        }
      }

      return notification
    } catch (error) {
      console.error('Failed to show notification:', error)
      // Fallback to toast
      toast(title)
    }
  }

  // Show expiry alert
  showExpiryAlert(items) {
    if (!items || items.length === 0) return

    const title = items.length === 1 
      ? `${items[0].name} is expiring soon!`
      : `${items.length} items are expiring soon!`

    const body = items.length === 1
      ? `Check your pantry - ${items[0].name} expires soon.`
      : `Check your pantry - multiple items are expiring soon.`

    this.showNotification(title, {
      body,
      icon: '⏰',
      tag: 'expiry-alert',
      requireInteraction: true,
      actions: [
        {
          action: 'view',
          title: 'View Pantry'
        },
        {
          action: 'dismiss',
          title: 'Dismiss'
        }
      ]
    })
  }

  // Show price drop alert
  showPriceDropAlert(item, oldPrice, newPrice, store) {
    const savings = oldPrice - newPrice
    const percentage = Math.round((savings / oldPrice) * 100)

    const title = `Price Drop Alert! 💰`
    const body = `${item} is now $${newPrice.toFixed(2)} at ${store} (${percentage}% off)`

    this.showNotification(title, {
      body,
      icon: '💰',
      tag: 'price-drop',
      requireInteraction: true,
      actions: [
        {
          action: 'view',
          title: 'View Prices'
        },
        {
          action: 'add-to-list',
          title: 'Add to List'
        }
      ]
    })
  }

  // Show shopping reminder
  showShoppingReminder(itemCount) {
    const title = 'Shopping Reminder 🛒'
    const body = `You have ${itemCount} items in your grocery list. Don't forget to shop!`

    this.showNotification(title, {
      body,
      icon: '🛒',
      tag: 'shopping-reminder',
      actions: [
        {
          action: 'view',
          title: 'View List'
        },
        {
          action: 'snooze',
          title: 'Remind Later'
        }
      ]
    })
  }

  // Schedule periodic notifications
  schedulePeriodicNotifications() {
    // Check for expiring items daily
    setInterval(() => {
      this.checkExpiringItems()
    }, 24 * 60 * 60 * 1000) // 24 hours

    // Shopping reminder (weekly)
    setInterval(() => {
      this.checkShoppingReminder()
    }, 7 * 24 * 60 * 60 * 1000) // 7 days
  }

  // Check for expiring items
  checkExpiringItems() {
    // This would be called from the pantry store
    // Implementation depends on how you want to integrate with stores
    console.log('Checking for expiring items...')
  }

  // Check if user needs shopping reminder
  checkShoppingReminder() {
    // This would be called from the grocery store
    // Implementation depends on how you want to integrate with stores
    console.log('Checking shopping reminder...')
  }

  // Show toast notification (in-app)
  showToast(message, type = 'default', options = {}) {
    const toastOptions = {
      duration: 4000,
      position: 'top-center',
      ...options
    }

    switch (type) {
      case 'success':
        return toast.success(message, toastOptions)
      case 'error':
        return toast.error(message, toastOptions)
      case 'loading':
        return toast.loading(message, toastOptions)
      case 'custom':
        return toast.custom(message, toastOptions)
      default:
        return toast(message, toastOptions)
    }
  }

  // Show persistent notification banner
  showBanner(message, type = 'info', actions = []) {
    // This could be implemented with a banner component
    // For now, use toast with longer duration
    const duration = 8000
    const icon = type === 'warning' ? '⚠️' : type === 'error' ? '❌' : 'ℹ️'
    
    return toast(message, {
      icon,
      duration,
      position: 'top-center'
    })
  }

  // Clear all notifications
  clearAll() {
    toast.dismiss()
  }

  // Get notification permission status
  getPermissionStatus() {
    return this.permission
  }

  // Check if notifications are supported
  isSupported() {
    return 'Notification' in window
  }
}

// Create singleton instance
const notificationService = new NotificationService()

export default notificationService

// Helper functions for easy access
export const showToast = (message, type, options) => 
  notificationService.showToast(message, type, options)

export const showNotification = (title, options) => 
  notificationService.showNotification(title, options)

export const showExpiryAlert = (items) => 
  notificationService.showExpiryAlert(items)

export const showPriceDropAlert = (item, oldPrice, newPrice, store) => 
  notificationService.showPriceDropAlert(item, oldPrice, newPrice, store)

export const requestNotificationPermission = () => 
  notificationService.requestPermission()
