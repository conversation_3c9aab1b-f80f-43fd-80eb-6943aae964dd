import { motion } from 'framer-motion'
import { Home, ArrowLeft, Search, ShoppingCart } from 'lucide-react'
import { Button } from '../components/UI/Button'
import { useAppStore } from '../stores/useStore'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
}

const floatingVariants = {
  animate: {
    y: [-10, 10, -10],
    rotate: [-5, 5, -5],
    transition: {
      duration: 4,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
}

export default function NotFound() {
  const { setCurrentPage } = useAppStore()

  const handleGoHome = () => {
    setCurrentPage('dashboard')
  }

  const handleGoBack = () => {
    window.history.back()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-secondary/5 to-primary/5 flex items-center justify-center p-4">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="text-center max-w-2xl mx-auto"
      >
        {/* 404 Illustration */}
        <motion.div
          variants={itemVariants}
          className="relative mb-8"
        >
          {/* Large 404 Text */}
          <motion.div
            className="text-[12rem] md:text-[16rem] font-black text-primary/10 leading-none select-none"
            animate={{
              scale: [1, 1.02, 1],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            404
          </motion.div>

          {/* Floating Elements */}
          <motion.div
            variants={floatingVariants}
            animate="animate"
            className="absolute top-8 left-8 w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center"
          >
            <ShoppingCart className="w-8 h-8 text-primary" />
          </motion.div>

          <motion.div
            variants={floatingVariants}
            animate="animate"
            style={{ animationDelay: '1s' }}
            className="absolute top-16 right-12 w-12 h-12 bg-secondary/20 rounded-lg flex items-center justify-center"
          >
            <Search className="w-6 h-6 text-secondary-600" />
          </motion.div>

          <motion.div
            variants={floatingVariants}
            animate="animate"
            style={{ animationDelay: '2s' }}
            className="absolute bottom-12 left-16 w-10 h-10 bg-warning-500/20 rounded-full"
          />

          <motion.div
            variants={floatingVariants}
            animate="animate"
            style={{ animationDelay: '0.5s' }}
            className="absolute bottom-8 right-8 w-8 h-8 bg-success-500/20 rounded-lg"
          />
        </motion.div>

        {/* Content */}
        <motion.div variants={itemVariants} className="space-y-6">
          <div className="space-y-2">
            <h1 className="text-4xl md:text-6xl font-bold gradient-text">
              Page Not Found
            </h1>
            <p className="text-xl text-muted-foreground max-w-md mx-auto">
              Oops! The page you're looking for seems to have wandered off like a shopping cart in a parking lot.
            </p>
          </div>

          <motion.div
            variants={itemVariants}
            className="bg-card border rounded-xl p-6 max-w-md mx-auto"
          >
            <h3 className="font-semibold mb-3">What you can do:</h3>
            <ul className="text-sm text-muted-foreground space-y-2 text-left">
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                <span>Check the URL for typos</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                <span>Go back to the previous page</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                <span>Return to the dashboard</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full" />
                <span>Start managing your groceries</span>
              </li>
            </ul>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button
              onClick={handleGoHome}
              size="lg"
              className="w-full sm:w-auto"
            >
              <Home className="w-4 h-4 mr-2" />
              Go to Dashboard
            </Button>
            
            <Button
              onClick={handleGoBack}
              variant="outline"
              size="lg"
              className="w-full sm:w-auto"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </Button>
          </motion.div>

          {/* Fun Facts */}
          <motion.div
            variants={itemVariants}
            className="mt-12 p-4 bg-muted/50 rounded-lg max-w-md mx-auto"
          >
            <p className="text-xs text-muted-foreground">
              💡 <strong>Fun fact:</strong> The average person spends 43 minutes per week grocery shopping. 
              With Grocify, you can make that time more efficient!
            </p>
          </motion.div>
        </motion.div>

        {/* Decorative Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-primary/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                scale: [0, 1, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 0.5,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </motion.div>
    </div>
  )
}
