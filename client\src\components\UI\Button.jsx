import { forwardRef } from 'react'
import { motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'
import { cn } from '../../lib/utils'

const buttonVariants = {
  default: "btn btn-primary",
  destructive: "btn btn-destructive",
  outline: "btn btn-outline",
  secondary: "btn btn-secondary",
  ghost: "btn btn-ghost",
  link: "underline-offset-4 hover:underline text-primary"
}

const buttonSizes = {
  default: "btn-md",
  sm: "btn-sm",
  lg: "btn-lg",
  icon: "h-10 w-10"
}

const Button = forwardRef(({ 
  className, 
  variant = "default", 
  size = "default", 
  loading = false,
  children,
  disabled,
  ...props 
}, ref) => {
  const isDisabled = disabled || loading

  return (
    <motion.button
      ref={ref}
      className={cn(
        buttonVariants[variant],
        buttonSizes[size],
        isDisabled && "opacity-50 cursor-not-allowed",
        className
      )}
      disabled={isDisabled}
      whileHover={!isDisabled ? { scale: 1.02 } : {}}
      whileTap={!isDisabled ? { scale: 0.98 } : {}}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
      {...props}
    >
      {loading && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="mr-2"
        >
          <Loader2 className="h-4 w-4 animate-spin" />
        </motion.div>
      )}
      {children}
    </motion.button>
  )
})

Button.displayName = "Button"

export { Button, buttonVariants }
