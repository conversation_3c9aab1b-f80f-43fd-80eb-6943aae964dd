import { Toaster as SonnerToaster, toast } from 'sonner'
import { 
  CheckCircle, 
  AlertCircle, 
  Info, 
  X, 
  AlertTriangle,
  Loader2 
} from 'lucide-react'
import { motion } from 'framer-motion'

// Custom toast component with animations
function CustomToast({ title, description, type, action, onDismiss }) {
  const icons = {
    success: <CheckCircle className="w-5 h-5 text-success-500" />,
    error: <AlertCircle className="w-5 h-5 text-danger-500" />,
    warning: <AlertTriangle className="w-5 h-5 text-warning-500" />,
    info: <Info className="w-5 h-5 text-primary" />,
    loading: <Loader2 className="w-5 h-5 text-primary animate-spin" />
  }

  const bgColors = {
    success: 'bg-success-50 border-success-200',
    error: 'bg-danger-50 border-danger-200',
    warning: 'bg-warning-50 border-warning-200',
    info: 'bg-primary-50 border-primary-200',
    loading: 'bg-secondary-50 border-secondary-200'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: 20, scale: 0.95 }}
      className={`
        relative flex items-start gap-3 p-4 rounded-lg border shadow-lg backdrop-blur-sm
        ${bgColors[type] || bgColors.info}
      `}
    >
      <div className="flex-shrink-0 mt-0.5">
        {icons[type] || icons.info}
      </div>
      
      <div className="flex-1 min-w-0">
        {title && (
          <div className="text-sm font-medium text-foreground mb-1">
            {title}
          </div>
        )}
        {description && (
          <div className="text-sm text-muted-foreground">
            {description}
          </div>
        )}
        {action && (
          <div className="mt-3">
            {action}
          </div>
        )}
      </div>

      {onDismiss && (
        <button
          onClick={onDismiss}
          className="flex-shrink-0 p-1 rounded-md hover:bg-black/5 transition-colors"
        >
          <X className="w-4 h-4 text-muted-foreground" />
        </button>
      )}
    </motion.div>
  )
}

// Enhanced toast functions
export const showToast = {
  success: (message, options = {}) => {
    return toast.custom((t) => (
      <CustomToast
        title={options.title || 'Success'}
        description={message}
        type="success"
        action={options.action}
        onDismiss={() => toast.dismiss(t)}
      />
    ), {
      duration: options.duration || 4000,
      ...options
    })
  },

  error: (message, options = {}) => {
    return toast.custom((t) => (
      <CustomToast
        title={options.title || 'Error'}
        description={message}
        type="error"
        action={options.action}
        onDismiss={() => toast.dismiss(t)}
      />
    ), {
      duration: options.duration || 6000,
      ...options
    })
  },

  warning: (message, options = {}) => {
    return toast.custom((t) => (
      <CustomToast
        title={options.title || 'Warning'}
        description={message}
        type="warning"
        action={options.action}
        onDismiss={() => toast.dismiss(t)}
      />
    ), {
      duration: options.duration || 5000,
      ...options
    })
  },

  info: (message, options = {}) => {
    return toast.custom((t) => (
      <CustomToast
        title={options.title || 'Info'}
        description={message}
        type="info"
        action={options.action}
        onDismiss={() => toast.dismiss(t)}
      />
    ), {
      duration: options.duration || 4000,
      ...options
    })
  },

  loading: (message, options = {}) => {
    return toast.custom((t) => (
      <CustomToast
        title={options.title || 'Loading'}
        description={message}
        type="loading"
        onDismiss={() => toast.dismiss(t)}
      />
    ), {
      duration: Infinity,
      ...options
    })
  },

  promise: (promise, options = {}) => {
    return toast.promise(promise, {
      loading: options.loading || 'Loading...',
      success: (data) => {
        return options.success?.(data) || 'Success!'
      },
      error: (error) => {
        return options.error?.(error) || 'Something went wrong!'
      },
      ...options
    })
  },

  custom: (component, options = {}) => {
    return toast.custom(component, options)
  }
}

// Toaster component with custom styling
export function Toaster() {
  return (
    <SonnerToaster
      position="top-right"
      expand={true}
      richColors={false}
      closeButton={false}
      toastOptions={{
        style: {
          background: 'transparent',
          border: 'none',
          boxShadow: 'none',
          padding: 0,
        },
        className: 'toast-custom',
      }}
      theme="light"
      offset="16px"
    />
  )
}

// Notification permission helper
export async function requestNotificationPermission() {
  if ('Notification' in window) {
    const permission = await Notification.requestPermission()
    if (permission === 'granted') {
      showToast.success('Notifications enabled!')
      return true
    } else {
      showToast.error('Notifications blocked. Enable in browser settings.')
      return false
    }
  }
  return false
}

// Browser notification helper
export function showBrowserNotification(title, options = {}) {
  if ('Notification' in window && Notification.permission === 'granted') {
    const notification = new Notification(title, {
      icon: '/pwa-192x192.png',
      badge: '/pwa-192x192.png',
      ...options
    })

    notification.onclick = () => {
      window.focus()
      notification.close()
      options.onClick?.()
    }

    return notification
  }
}

// Toast hook for components
export function useToast() {
  return {
    toast: showToast,
    dismiss: toast.dismiss,
    dismissAll: () => toast.dismiss()
  }
}

// Example usage
export function ToastExample() {
  const handleShowToasts = () => {
    showToast.success('Item added to grocery list!')
    
    setTimeout(() => {
      showToast.warning('Item expires in 3 days', {
        action: (
          <button className="text-sm bg-warning-500 text-white px-3 py-1 rounded-md hover:bg-warning-600 transition-colors">
            View Item
          </button>
        )
      })
    }, 1000)

    setTimeout(() => {
      showToast.info('Price drop alert!', {
        title: 'Great Deal',
        action: (
          <div className="flex gap-2">
            <button className="text-sm bg-primary text-white px-3 py-1 rounded-md hover:bg-primary/90 transition-colors">
              View
            </button>
            <button className="text-sm border border-primary text-primary px-3 py-1 rounded-md hover:bg-primary/10 transition-colors">
              Dismiss
            </button>
          </div>
        )
      })
    }, 2000)
  }

  const handlePromiseToast = () => {
    const promise = new Promise((resolve, reject) => {
      setTimeout(() => {
        Math.random() > 0.5 ? resolve('Success!') : reject('Failed!')
      }, 2000)
    })

    showToast.promise(promise, {
      loading: 'Saving item...',
      success: 'Item saved successfully!',
      error: 'Failed to save item'
    })
  }

  return (
    <div className="space-y-4 p-6">
      <h3 className="text-lg font-semibold">Toast Examples</h3>
      <div className="flex flex-wrap gap-2">
        <button
          onClick={handleShowToasts}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
        >
          Show Multiple Toasts
        </button>
        <button
          onClick={handlePromiseToast}
          className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80 transition-colors"
        >
          Promise Toast
        </button>
        <button
          onClick={() => showToast.error('This is an error message')}
          className="px-4 py-2 bg-danger-500 text-white rounded-md hover:bg-danger-600 transition-colors"
        >
          Error Toast
        </button>
      </div>
    </div>
  )
}
