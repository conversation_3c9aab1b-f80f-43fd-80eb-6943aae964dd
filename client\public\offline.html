<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grocify - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #f1f5f9 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            text-align: center;
            max-width: 400px;
            background: white;
            padding: 40px 30px;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .icon {
            width: 80px;
            height: 80px;
            background: #0ea5e9;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
        }
        
        .icon svg {
            width: 40px;
            height: 40px;
            color: white;
        }
        
        h1 {
            color: #1e293b;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 12px;
        }
        
        p {
            color: #64748b;
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 24px;
        }
        
        .features {
            text-align: left;
            margin: 24px 0;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: #475569;
            font-size: 14px;
        }
        
        .feature-icon {
            width: 16px;
            height: 16px;
            background: #22c55e;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .feature-icon::after {
            content: '✓';
            color: white;
            font-size: 10px;
            font-weight: bold;
        }
        
        .retry-btn {
            background: #0ea5e9;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .retry-btn:hover {
            background: #0284c7;
        }
        
        .status {
            margin-top: 20px;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .status.offline {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fbbf24;
        }
        
        .status.online {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #22c55e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7Z"/>
                <path d="M9 8V17H11V8H9ZM13 8V17H15V8H13Z"/>
            </svg>
        </div>
        
        <h1>You're Offline</h1>
        <p>Don't worry! Grocify works offline. You can still access your saved data and continue managing your groceries.</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon"></div>
                View your grocery lists
            </div>
            <div class="feature">
                <div class="feature-icon"></div>
                Check pantry inventory
            </div>
            <div class="feature">
                <div class="feature-icon"></div>
                Add new items
            </div>
            <div class="feature">
                <div class="feature-icon"></div>
                Track expiry dates
            </div>
        </div>
        
        <button class="retry-btn" onclick="checkConnection()">
            Check Connection
        </button>
        
        <div id="status" class="status offline">
            📡 Currently offline
        </div>
    </div>

    <script>
        function checkConnection() {
            const status = document.getElementById('status');
            
            if (navigator.onLine) {
                status.className = 'status online';
                status.innerHTML = '🌐 Back online! Redirecting...';
                
                setTimeout(() => {
                    window.location.href = '/';
                }, 1500);
            } else {
                status.className = 'status offline';
                status.innerHTML = '📡 Still offline - check your connection';
            }
        }
        
        // Auto-check connection when it comes back
        window.addEventListener('online', () => {
            checkConnection();
        });
        
        // Update status on page load
        window.addEventListener('load', () => {
            checkConnection();
        });
    </script>
</body>
</html>
