import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Check, AlertCircle, Loader2 } from 'lucide-react'
import { Button } from './Button'
import { Input, Textarea } from './Input'
import { cn } from '../../lib/utils'

// Form validation rules
const validationRules = {
  required: (value) => value && value.trim() !== '' ? null : 'This field is required',
  email: (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(value) ? null : 'Please enter a valid email address'
  },
  minLength: (min) => (value) => 
    value && value.length >= min ? null : `Must be at least ${min} characters`,
  maxLength: (max) => (value) => 
    value && value.length <= max ? null : `Must be no more than ${max} characters`,
  number: (value) => {
    const num = parseFloat(value)
    return !isNaN(num) && isFinite(num) ? null : 'Please enter a valid number'
  },
  positive: (value) => {
    const num = parseFloat(value)
    return num > 0 ? null : 'Must be a positive number'
  },
  url: (value) => {
    try {
      new URL(value)
      return null
    } catch {
      return 'Please enter a valid URL'
    }
  }
}

// Form hook for managing form state and validation
export function useForm(initialValues = {}, validationSchema = {}) {
  const [values, setValues] = useState(initialValues)
  const [errors, setErrors] = useState({})
  const [touched, setTouched] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const validateField = (name, value) => {
    const rules = validationSchema[name]
    if (!rules) return null

    for (const rule of rules) {
      const error = rule(value)
      if (error) return error
    }
    return null
  }

  const validateForm = () => {
    const newErrors = {}
    let isValid = true

    Object.keys(validationSchema).forEach(name => {
      const error = validateField(name, values[name])
      if (error) {
        newErrors[name] = error
        isValid = false
      }
    })

    setErrors(newErrors)
    return isValid
  }

  const setValue = (name, value) => {
    setValues(prev => ({ ...prev, [name]: value }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }))
    }
  }

  const setFieldTouched = (name) => {
    setTouched(prev => ({ ...prev, [name]: true }))
    
    // Validate field when it loses focus
    const error = validateField(name, values[name])
    setErrors(prev => ({ ...prev, [name]: error }))
  }

  const handleSubmit = async (onSubmit) => {
    setIsSubmitting(true)
    
    // Mark all fields as touched
    const allTouched = Object.keys(validationSchema).reduce((acc, key) => {
      acc[key] = true
      return acc
    }, {})
    setTouched(allTouched)

    if (validateForm()) {
      try {
        await onSubmit(values)
      } catch (error) {
        console.error('Form submission error:', error)
      }
    }
    
    setIsSubmitting(false)
  }

  const reset = () => {
    setValues(initialValues)
    setErrors({})
    setTouched({})
    setIsSubmitting(false)
  }

  return {
    values,
    errors,
    touched,
    isSubmitting,
    setValue,
    setFieldTouched,
    handleSubmit,
    reset,
    isValid: Object.keys(errors).length === 0
  }
}

// Form field component
function FormField({ 
  name, 
  label, 
  type = "text",
  placeholder,
  helperText,
  required = false,
  floating = false,
  leftIcon,
  rightIcon,
  form,
  className,
  ...props 
}) {
  const { values, errors, touched, setValue, setFieldTouched } = form
  const error = touched[name] ? errors[name] : null
  const hasError = !!error
  const hasSuccess = touched[name] && !error && values[name]

  return (
    <div className={cn("space-y-1", className)}>
      {type === 'textarea' ? (
        <Textarea
          label={label}
          placeholder={placeholder}
          value={values[name] || ''}
          error={error}
          helperText={helperText}
          onChange={(e) => setValue(name, e.target.value)}
          onBlur={() => setFieldTouched(name)}
          {...props}
        />
      ) : (
        <Input
          type={type}
          label={label}
          placeholder={placeholder}
          value={values[name] || ''}
          error={error}
          success={hasSuccess}
          helperText={helperText}
          floating={floating}
          leftIcon={leftIcon}
          rightIcon={rightIcon}
          onChange={(e) => setValue(name, e.target.value)}
          onBlur={() => setFieldTouched(name)}
          {...props}
        />
      )}
    </div>
  )
}

// Animated form container
function Form({ 
  children, 
  onSubmit, 
  className,
  title,
  description,
  ...props 
}) {
  return (
    <motion.form
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      onSubmit={(e) => {
        e.preventDefault()
        onSubmit?.(e)
      }}
      className={cn("space-y-6", className)}
      {...props}
    >
      {(title || description) && (
        <div className="space-y-2">
          {title && (
            <h2 className="text-2xl font-bold tracking-tight">{title}</h2>
          )}
          {description && (
            <p className="text-muted-foreground">{description}</p>
          )}
        </div>
      )}
      {children}
    </motion.form>
  )
}

// Form section for grouping fields
function FormSection({ title, description, children, className }) {
  return (
    <div className={cn("space-y-4", className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg font-medium">{title}</h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )
}

// Form actions (submit, cancel, etc.)
function FormActions({ children, className, ...props }) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.2 }}
      className={cn("flex flex-col sm:flex-row gap-3 pt-4", className)}
      {...props}
    >
      {children}
    </motion.div>
  )
}

// Submit button with loading state
function SubmitButton({ 
  children = "Submit", 
  loading = false, 
  disabled = false,
  className,
  ...props 
}) {
  return (
    <Button
      type="submit"
      loading={loading}
      disabled={disabled || loading}
      className={cn("w-full sm:w-auto", className)}
      {...props}
    >
      {children}
    </Button>
  )
}

// Example usage component
function ExampleForm() {
  const form = useForm(
    { name: '', email: '', message: '' },
    {
      name: [validationRules.required, validationRules.minLength(2)],
      email: [validationRules.required, validationRules.email],
      message: [validationRules.required, validationRules.minLength(10)]
    }
  )

  const handleSubmit = async (values) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    console.log('Form submitted:', values)
    form.reset()
  }

  return (
    <Form
      title="Contact Us"
      description="Send us a message and we'll get back to you."
      onSubmit={() => form.handleSubmit(handleSubmit)}
      className="max-w-md mx-auto"
    >
      <FormSection>
        <FormField
          name="name"
          label="Full Name"
          placeholder="Enter your name"
          floating
          form={form}
        />
        
        <FormField
          name="email"
          type="email"
          label="Email Address"
          placeholder="Enter your email"
          floating
          form={form}
        />
        
        <FormField
          name="message"
          type="textarea"
          label="Message"
          placeholder="Enter your message"
          helperText="Minimum 10 characters"
          form={form}
        />
      </FormSection>

      <FormActions>
        <SubmitButton loading={form.isSubmitting}>
          Send Message
        </SubmitButton>
        <Button type="button" variant="outline" onClick={form.reset}>
          Reset
        </Button>
      </FormActions>
    </Form>
  )
}

export { 
  Form, 
  FormField, 
  FormSection, 
  FormActions, 
  SubmitButton, 
  useForm, 
  validationRules,
  ExampleForm 
}
