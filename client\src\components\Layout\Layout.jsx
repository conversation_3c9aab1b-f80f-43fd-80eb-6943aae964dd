import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Navbar from './Navbar'
import PWAInstallPrompt from '../PWA/PWAInstallPrompt'
import NotificationManager from '../Notifications/NotificationManager'
import { Toaster } from '../UI/Toast'
import { Preloader } from '../UI/Preloader'
import { useAppStore } from '../../stores/useStore'
import { cn } from '../../lib/utils'

const pageVariants = {
  initial: { opacity: 0, y: 20 },
  in: { opacity: 1, y: 0 },
  out: { opacity: 0, y: -20 }
}

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.3
}

export default function Layout({ children }) {
  const { theme, isOnline, setIsOnline, setDeferredPrompt } = useAppStore()
  const [isLoading, setIsLoading] = useState(true)

  // Handle initial loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1500)

    return () => clearTimeout(timer)
  }, [])

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [setIsOnline])

  // Handle PWA install prompt
  useEffect(() => {
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault()
      setDeferredPrompt(e)
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    }
  }, [setDeferredPrompt])

  // Apply theme class to document
  useEffect(() => {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [theme])

  // Show preloader on initial load
  if (isLoading) {
    return <Preloader isLoading={isLoading} onComplete={() => setIsLoading(false)} />
  }

  return (
    <div className={cn("min-h-screen bg-background transition-colors duration-300", theme)}>
      {/* Navigation */}
      <Navbar />

      {/* Offline Indicator */}
      <AnimatePresence>
        {!isOnline && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-16 left-0 right-0 z-40 bg-warning-500 text-white text-center py-3 text-sm font-medium shadow-lg backdrop-blur-sm"
          >
            <div className="container flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
              <span>You're offline. Some features may be limited.</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <main className="pt-16 pb-20 md:pb-8 min-h-screen">
        <AnimatePresence mode="wait">
          <motion.div
            key={location.pathname}
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
            className="page-transition"
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </main>

      {/* PWA Install Prompt */}
      <PWAInstallPrompt />

      {/* Notification Manager */}
      <NotificationManager />

      {/* Enhanced Toast Notifications */}
      <Toaster />
    </div>
  )
}
