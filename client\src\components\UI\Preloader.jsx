import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ShoppingCart } from 'lucide-react'

const preloaderVariants = {
  initial: { opacity: 1 },
  exit: { 
    opacity: 0,
    scale: 0.8,
    transition: { 
      duration: 0.5,
      ease: "easeInOut"
    }
  }
}

const logoVariants = {
  initial: { scale: 0.8, opacity: 0 },
  animate: { 
    scale: 1, 
    opacity: 1,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
}

const textVariants = {
  initial: { y: 20, opacity: 0 },
  animate: { 
    y: 0, 
    opacity: 1,
    transition: {
      delay: 0.3,
      duration: 0.5,
      ease: "easeOut"
    }
  }
}

const progressVariants = {
  initial: { width: 0 },
  animate: { 
    width: "100%",
    transition: {
      delay: 0.5,
      duration: 2,
      ease: "easeInOut"
    }
  }
}

function Preloader({ isLoading = true, onComplete }) {
  const [progress, setProgress] = useState(0)
  const [showPreloader, setShowPreloader] = useState(isLoading)

  useEffect(() => {
    if (!isLoading) return

    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer)
          setTimeout(() => {
            setShowPreloader(false)
            onComplete?.()
          }, 500)
          return 100
        }
        return prev + Math.random() * 15
      })
    }, 100)

    return () => clearInterval(timer)
  }, [isLoading, onComplete])

  return (
    <AnimatePresence>
      {showPreloader && (
        <motion.div
          variants={preloaderVariants}
          initial="initial"
          animate="initial"
          exit="exit"
          className="fixed inset-0 z-[9999] flex flex-col items-center justify-center bg-background"
        >
          {/* Logo */}
          <motion.div
            variants={logoVariants}
            initial="initial"
            animate="animate"
            className="mb-8"
          >
            <div className="relative">
              <motion.div
                className="w-20 h-20 bg-primary rounded-2xl flex items-center justify-center shadow-large"
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <ShoppingCart className="w-10 h-10 text-primary-foreground" />
              </motion.div>
              
              {/* Glow effect */}
              <motion.div
                className="absolute inset-0 w-20 h-20 bg-primary rounded-2xl opacity-20"
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.2, 0.5, 0.2]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
            </div>
          </motion.div>

          {/* App Name */}
          <motion.div
            variants={textVariants}
            initial="initial"
            animate="animate"
            className="text-center mb-8"
          >
            <h1 className="text-4xl font-bold gradient-text mb-2">
              Grocify
            </h1>
            <p className="text-muted-foreground text-lg">
              Smart Grocery Management
            </p>
          </motion.div>

          {/* Progress Bar */}
          <motion.div
            variants={textVariants}
            initial="initial"
            animate="animate"
            className="w-64 space-y-2"
          >
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Loading...</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="h-2 bg-secondary rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-primary to-primary/60 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              />
            </div>
          </motion.div>

          {/* Loading Dots */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
            className="flex space-x-1 mt-8"
          >
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-primary rounded-full"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Simple loading overlay
function LoadingOverlay({ isLoading, children, className }) {
  return (
    <div className={`relative ${className}`}>
      {children}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50"
          >
            <div className="text-center space-y-4">
              <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
              <p className="text-sm text-muted-foreground">Loading...</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Splash screen for PWA
function SplashScreen({ isVisible = true, onComplete }) {
  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        onComplete?.()
      }, 2000)
      return () => clearTimeout(timer)
    }
  }, [isVisible, onComplete])

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 1 }}
          exit={{ 
            opacity: 0,
            scale: 1.1,
            transition: { duration: 0.5 }
          }}
          className="fixed inset-0 z-[9999] bg-gradient-to-br from-primary to-primary/80 flex flex-col items-center justify-center text-primary-foreground"
        >
          <motion.div
            initial={{ scale: 0.5, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ 
              delay: 0.2,
              type: "spring",
              stiffness: 200,
              damping: 20
            }}
            className="text-center"
          >
            <div className="w-24 h-24 bg-white/20 rounded-3xl flex items-center justify-center mb-6 mx-auto backdrop-blur-sm">
              <ShoppingCart className="w-12 h-12" />
            </div>
            <h1 className="text-5xl font-bold mb-2">Grocify</h1>
            <p className="text-xl opacity-90">Smart Grocery Management</p>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export { Preloader, LoadingOverlay, SplashScreen }
