import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import Layout from './components/Layout/Layout'
import ErrorBoundary from './components/Common/ErrorBoundary'
import Landing from './pages/Landing'
import Dashboard from './pages/Dashboard'
import GroceryList from './pages/GroceryList'
import PriceTracker from './pages/PriceTracker'
import Pantry from './pages/Pantry'
import Profile from './pages/Profile'
import Settings from './pages/Settings'
import NotFound from './pages/NotFound'
import { useAppStore } from './stores/useStore'

function AppContent() {
  const { currentPage } = useAppStore()

  // Simple page routing based on store state
  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />
      case 'grocery':
        return <GroceryList />
      case 'price-tracker':
        return <PriceTracker />
      case 'pantry':
        return <Pantry />
      case 'profile':
        return <Profile />
      case 'settings':
        return <Settings />
      case '404':
        return <NotFound />
      default:
        return <Landing />
    }
  }

  return (
    <Layout>
      {renderPage()}
    </Layout>
  )
}

function App() {
  return (
    <ErrorBoundary>
      <Router>
        <Routes>
          <Route path="/" element={<AppContent />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    </ErrorBoundary>
  )
}

export default App
