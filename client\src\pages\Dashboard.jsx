import { motion } from 'framer-motion'
import { 
  ShoppingCart, 
  TrendingUp, 
  Package, 
  Plus,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'
import { useGroceryStore, usePantryStore, usePriceStore } from '../stores/useStore'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
}

export default function Dashboard() {
  const { items: groceryItems } = useGroceryStore()
  const { items: pantryItems, getExpiringItems, getExpiredItems } = usePantryStore()
  const { prices } = usePriceStore()

  const completedItems = groceryItems.filter(item => item.completed).length
  const totalItems = groceryItems.length
  const expiringItems = getExpiringItems(7)
  const expiredItems = getExpiredItems()

  const stats = [
    {
      title: 'Grocery Items',
      value: totalItems,
      subtitle: `${completedItems} completed`,
      icon: ShoppingCart,
      color: 'primary',
      progress: totalItems > 0 ? (completedItems / totalItems) * 100 : 0
    },
    {
      title: 'Pantry Items',
      value: pantryItems.length,
      subtitle: `${expiredItems.length} expired`,
      icon: Package,
      color: 'success',
      alert: expiredItems.length > 0
    },
    {
      title: 'Price Entries',
      value: prices.length,
      subtitle: 'Tracked items',
      icon: TrendingUp,
      color: 'warning'
    }
  ]

  return (
    <motion.div 
      className="container py-8"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900 mb-2">
          Welcome back! 👋
        </h1>
        <p className="text-secondary-600">
          Here's what's happening with your groceries today.
        </p>
      </motion.div>

      {/* Stats Grid */}
      <motion.div 
        variants={itemVariants}
        className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
      >
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <motion.div
              key={index}
              className="card hover:shadow-md transition-shadow duration-300"
              whileHover={{ y: -2 }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-${stat.color}-100 rounded-lg flex items-center justify-center`}>
                  <Icon className={`w-6 h-6 text-${stat.color}-600`} />
                </div>
                {stat.alert && (
                  <AlertTriangle className="w-5 h-5 text-danger-500" />
                )}
              </div>
              
              <div className="space-y-2">
                <div className="flex items-baseline space-x-2">
                  <span className="text-2xl font-bold text-secondary-900">
                    {stat.value}
                  </span>
                  <span className="text-sm text-secondary-500">
                    {stat.title}
                  </span>
                </div>
                
                <p className="text-sm text-secondary-600">
                  {stat.subtitle}
                </p>
                
                {stat.progress !== undefined && (
                  <div className="w-full bg-secondary-200 rounded-full h-2">
                    <div 
                      className={`bg-${stat.color}-600 h-2 rounded-full transition-all duration-300`}
                      style={{ width: `${stat.progress}%` }}
                    />
                  </div>
                )}
              </div>
            </motion.div>
          )
        })}
      </motion.div>

      {/* Quick Actions */}
      <motion.div variants={itemVariants} className="mb-8">
        <h2 className="text-xl font-semibold text-secondary-900 mb-4">
          Quick Actions
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { label: 'Add Grocery Item', icon: Plus, color: 'primary' },
            { label: 'Track Price', icon: TrendingUp, color: 'warning' },
            { label: 'Add to Pantry', icon: Package, color: 'success' },
            { label: 'View Lists', icon: ShoppingCart, color: 'secondary' }
          ].map((action, index) => {
            const Icon = action.icon
            return (
              <motion.button
                key={index}
                className={`card text-center p-4 hover:shadow-md transition-all duration-300 border-2 border-transparent hover:border-${action.color}-200`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className={`w-10 h-10 bg-${action.color}-100 rounded-lg flex items-center justify-center mx-auto mb-2`}>
                  <Icon className={`w-5 h-5 text-${action.color}-600`} />
                </div>
                <span className="text-sm font-medium text-secondary-700">
                  {action.label}
                </span>
              </motion.button>
            )
          })}
        </div>
      </motion.div>

      {/* Alerts & Notifications */}
      {(expiringItems.length > 0 || expiredItems.length > 0) && (
        <motion.div variants={itemVariants} className="mb-8">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4">
            Alerts & Notifications
          </h2>
          <div className="space-y-3">
            {expiredItems.length > 0 && (
              <motion.div 
                className="bg-danger-50 border border-danger-200 rounded-lg p-4"
                whileHover={{ scale: 1.01 }}
              >
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-5 h-5 text-danger-600" />
                  <div>
                    <h3 className="font-medium text-danger-900">
                      {expiredItems.length} item{expiredItems.length > 1 ? 's' : ''} expired
                    </h3>
                    <p className="text-sm text-danger-700">
                      Check your pantry and remove expired items
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
            
            {expiringItems.length > 0 && (
              <motion.div 
                className="bg-warning-50 border border-warning-200 rounded-lg p-4"
                whileHover={{ scale: 1.01 }}
              >
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-warning-600" />
                  <div>
                    <h3 className="font-medium text-warning-900">
                      {expiringItems.length} item{expiringItems.length > 1 ? 's' : ''} expiring soon
                    </h3>
                    <p className="text-sm text-warning-700">
                      Use these items within the next 7 days
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      )}

      {/* Recent Activity */}
      <motion.div variants={itemVariants}>
        <h2 className="text-xl font-semibold text-secondary-900 mb-4">
          Recent Activity
        </h2>
        <div className="card">
          {groceryItems.length === 0 && pantryItems.length === 0 ? (
            <div className="text-center py-8">
              <Package className="w-12 h-12 text-secondary-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-secondary-500 mb-2">
                No activity yet
              </h3>
              <p className="text-secondary-400">
                Start by adding items to your grocery list or pantry
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {groceryItems.slice(0, 3).map((item) => (
                <div key={item.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-secondary-50">
                  <CheckCircle className={`w-4 h-4 ${item.completed ? 'text-success-500' : 'text-secondary-300'}`} />
                  <span className={`flex-1 ${item.completed ? 'line-through text-secondary-500' : 'text-secondary-900'}`}>
                    {item.name}
                  </span>
                  <span className="text-xs text-secondary-400">
                    {new Date(item.addedAt).toLocaleDateString()}
                  </span>
                </div>
              ))}
              
              {groceryItems.length > 3 && (
                <div className="text-center pt-2">
                  <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                    View all items
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </motion.div>
    </motion.div>
  )
}
