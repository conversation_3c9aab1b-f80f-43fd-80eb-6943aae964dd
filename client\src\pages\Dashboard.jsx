import { motion } from 'framer-motion'
import {
  ShoppingCart,
  TrendingUp,
  Package,
  Plus,
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar,
  BarChart3
} from 'lucide-react'
import { useGroceryStore, usePantryStore, usePriceStore } from '../stores/useStore'
import { Button } from '../components/UI/Button'
import { Card } from '../components/UI/Card'
import { Progress } from '../components/UI/Progress'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
}

export default function Dashboard() {
  const { items: groceryItems } = useGroceryStore()
  const { items: pantryItems, getExpiringItems, getExpiredItems } = usePantryStore()
  const { prices } = usePriceStore()

  const completedItems = groceryItems.filter(item => item.completed).length
  const totalItems = groceryItems.length
  const expiringItems = getExpiringItems(7)
  const expiredItems = getExpiredItems()

  const stats = [
    {
      title: 'Grocery Items',
      value: totalItems,
      subtitle: `${completedItems} completed`,
      icon: ShoppingCart,
      color: 'primary',
      progress: totalItems > 0 ? (completedItems / totalItems) * 100 : 0
    },
    {
      title: 'Pantry Items',
      value: pantryItems.length,
      subtitle: `${expiredItems.length} expired`,
      icon: Package,
      color: 'success',
      alert: expiredItems.length > 0
    },
    {
      title: 'Price Entries',
      value: prices.length,
      subtitle: 'Tracked items',
      icon: TrendingUp,
      color: 'warning'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50/30 via-white to-purple-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <motion.div
        className="container py-8 max-w-7xl mx-auto"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {/* Enhanced Header */}
        <motion.div variants={itemVariants} className="relative mb-12">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10 rounded-3xl blur-3xl"></div>
          <div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              <div className="flex items-center gap-6">
                <motion.div
                  className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <ShoppingCart className="w-8 h-8 text-white" />
                </motion.div>
                <div>
                  <motion.h1
                    className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent mb-2"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    Welcome back! 👋
                  </motion.h1>
                  <motion.p
                    className="text-lg text-gray-600 dark:text-gray-300"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    Here's what's happening with your groceries today.
                  </motion.p>
                </div>
              </div>
              <motion.div
                className="flex gap-3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Button
                  variant="outline"
                  className="bg-white/50 hover:bg-white/80 border-white/30 backdrop-blur-sm transition-all duration-300 hover:scale-105"
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  Today
                </Button>
                <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <Plus className="w-4 h-4 mr-2" />
                  Quick Add
                </Button>
              </motion.div>
            </div>
          </div>
        </motion.div>

      {/* Stats Grid */}
      <motion.div 
        variants={itemVariants}
        className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
      >
        {stats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <motion.div
              key={index}
              className="group relative"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, type: "spring", stiffness: 300 }}
              whileHover={{ y: -8, transition: { duration: 0.3 } }}
            >
              {/* Glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              {/* Card */}
              <div className="relative bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl p-8 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500">
                <div className="flex items-start justify-between mb-6">
                  <motion.div
                    className={`p-4 rounded-2xl bg-gradient-to-br ${
                      stat.color === 'primary' ? 'from-blue-500 to-blue-600' :
                      stat.color === 'success' ? 'from-green-500 to-green-600' :
                      stat.color === 'warning' ? 'from-yellow-500 to-orange-500' :
                      'from-purple-500 to-purple-600'
                    } shadow-lg`}
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 400 }}
                  >
                    <Icon className="w-8 h-8 text-white" />
                  </motion.div>
                  {stat.alert && (
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <AlertTriangle className="w-6 h-6 text-red-500" />
                    </motion.div>
                  )}
                </div>

                <div className="space-y-4">
                  <motion.h3
                    className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent"
                    initial={{ scale: 0.5 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: index * 0.1 + 0.3, type: "spring", stiffness: 300 }}
                  >
                    {stat.value}
                  </motion.h3>
                  <div>
                    <p className="text-lg font-semibold text-gray-800 dark:text-gray-200">{stat.title}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{stat.subtitle}</p>
                  </div>

                  {stat.progress !== undefined && (
                    <div className="mt-6">
                      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                        <span>Progress</span>
                        <span className="font-semibold">{Math.round(stat.progress)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                        <motion.div
                          className={`h-full rounded-full bg-gradient-to-r ${
                            stat.color === 'primary' ? 'from-blue-500 to-blue-600' :
                            stat.color === 'success' ? 'from-green-500 to-green-600' :
                            stat.color === 'warning' ? 'from-yellow-500 to-orange-500' :
                            'from-purple-500 to-purple-600'
                          }`}
                          initial={{ width: 0 }}
                          animate={{ width: `${stat.progress}%` }}
                          transition={{ duration: 1.5, delay: index * 0.2 + 0.5, ease: "easeOut" }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          )
        })}
      </motion.div>

      {/* Progress Overview */}
      <motion.div variants={itemVariants} className="mb-8">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-foreground">Shopping Progress</h3>
              <p className="text-sm text-muted-foreground">Track your grocery list completion</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-foreground">{Math.round(stats[0].progress)}%</div>
              <div className="text-sm text-muted-foreground">Complete</div>
            </div>
          </div>

          <Progress
            value={stats[0].progress}
            className="mb-4"
            variant="default"
            size="lg"
            animated
            striped
          />

          <div className="grid grid-cols-2 gap-4 text-center">
            <div className="p-3 bg-green-50 rounded-lg">
              <div className="text-lg font-semibold text-green-600">{completedItems}</div>
              <div className="text-xs text-green-600">Completed</div>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="text-lg font-semibold text-blue-600">{totalItems - completedItems}</div>
              <div className="text-xs text-blue-600">Remaining</div>
            </div>
          </div>
        </Card>
      </motion.div>

        {/* Enhanced Quick Actions */}
        <motion.div variants={itemVariants} className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent mb-4">
              Quick Actions
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400">
              Get things done faster with these shortcuts
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { label: 'Add Grocery Item', icon: Plus, gradient: 'from-blue-500 to-blue-600' },
              { label: 'Track Price', icon: TrendingUp, gradient: 'from-yellow-500 to-orange-500' },
              { label: 'Add to Pantry', icon: Package, gradient: 'from-green-500 to-green-600' },
              { label: 'View Lists', icon: ShoppingCart, gradient: 'from-purple-500 to-purple-600' }
            ].map((action, index) => {
              const Icon = action.icon
              return (
                <motion.div
                  key={index}
                  className="group relative"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 + 0.5 }}
                  whileHover={{ y: -8, transition: { duration: 0.3 } }}
                >
                  {/* Glow effect */}
                  <div className={`absolute inset-0 bg-gradient-to-r ${action.gradient} rounded-2xl blur-xl opacity-0 group-hover:opacity-20 transition-opacity duration-500`}></div>

                  {/* Card */}
                  <motion.button
                    className="relative w-full bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl p-8 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500 text-center"
                    whileTap={{ scale: 0.95 }}
                  >
                    <motion.div
                      className={`w-16 h-16 bg-gradient-to-br ${action.gradient} rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg`}
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ type: "spring", stiffness: 400 }}
                    >
                      <Icon className="w-8 h-8 text-white" />
                    </motion.div>

                    <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {action.label}
                    </h3>
                  </motion.button>
                </motion.div>
              )
            })}
          </div>
        </motion.div>

      {/* Alerts & Notifications */}
      {(expiringItems.length > 0 || expiredItems.length > 0) && (
        <motion.div variants={itemVariants} className="mb-8">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4">
            Alerts & Notifications
          </h2>
          <div className="space-y-3">
            {expiredItems.length > 0 && (
              <motion.div 
                className="bg-danger-50 border border-danger-200 rounded-lg p-4"
                whileHover={{ scale: 1.01 }}
              >
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-5 h-5 text-danger-600" />
                  <div>
                    <h3 className="font-medium text-danger-900">
                      {expiredItems.length} item{expiredItems.length > 1 ? 's' : ''} expired
                    </h3>
                    <p className="text-sm text-danger-700">
                      Check your pantry and remove expired items
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
            
            {expiringItems.length > 0 && (
              <motion.div 
                className="bg-warning-50 border border-warning-200 rounded-lg p-4"
                whileHover={{ scale: 1.01 }}
              >
                <div className="flex items-center space-x-3">
                  <Clock className="w-5 h-5 text-warning-600" />
                  <div>
                    <h3 className="font-medium text-warning-900">
                      {expiringItems.length} item{expiringItems.length > 1 ? 's' : ''} expiring soon
                    </h3>
                    <p className="text-sm text-warning-700">
                      Use these items within the next 7 days
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      )}

      {/* Recent Activity */}
      <motion.div variants={itemVariants}>
        <h2 className="text-xl font-semibold text-secondary-900 mb-4">
          Recent Activity
        </h2>
        <div className="card">
          {groceryItems.length === 0 && pantryItems.length === 0 ? (
            <div className="text-center py-8">
              <Package className="w-12 h-12 text-secondary-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-secondary-500 mb-2">
                No activity yet
              </h3>
              <p className="text-secondary-400">
                Start by adding items to your grocery list or pantry
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {groceryItems.slice(0, 3).map((item) => (
                <div key={item.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-secondary-50">
                  <CheckCircle className={`w-4 h-4 ${item.completed ? 'text-success-500' : 'text-secondary-300'}`} />
                  <span className={`flex-1 ${item.completed ? 'line-through text-secondary-500' : 'text-secondary-900'}`}>
                    {item.name}
                  </span>
                  <span className="text-xs text-secondary-400">
                    {new Date(item.addedAt).toLocaleDateString()}
                  </span>
                </div>
              ))}
              
              {groceryItems.length > 3 && (
                <div className="text-center pt-2">
                  <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                    View all items
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
}
