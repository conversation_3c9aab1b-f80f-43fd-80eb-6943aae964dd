import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Download, X, Smartphone } from 'lucide-react'
import { useAppStore } from '../../stores/useStore'
import toast from 'react-hot-toast'

export default function PWAInstallPrompt() {
  const [showPrompt, setShowPrompt] = useState(false)
  const [isIOS, setIsIOS] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)
  const { deferredPrompt, setDeferredPrompt } = useAppStore()

  useEffect(() => {
    // Check if device is iOS
    const iOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
    setIsIOS(iOS)

    // Check if app is already installed (standalone mode)
    const standalone = window.matchMedia('(display-mode: standalone)').matches
    setIsStandalone(standalone)

    // Show prompt after a delay if conditions are met
    const timer = setTimeout(() => {
      if (!standalone && (deferredPrompt || iOS)) {
        setShowPrompt(true)
      }
    }, 5000) // Show after 5 seconds

    return () => clearTimeout(timer)
  }, [deferredPrompt])

  const handleInstall = async () => {
    if (deferredPrompt) {
      // For Android/Chrome
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        toast.success('App installed successfully!')
      }
      
      setDeferredPrompt(null)
      setShowPrompt(false)
    } else if (isIOS) {
      // For iOS, show instructions
      toast.success(
        'To install: Tap the Share button and select "Add to Home Screen"',
        { duration: 6000 }
      )
      setShowPrompt(false)
    }
  }

  const handleDismiss = () => {
    setShowPrompt(false)
    // Don't show again for this session
    sessionStorage.setItem('pwa-prompt-dismissed', 'true')
  }

  // Don't show if already dismissed this session
  useEffect(() => {
    const dismissed = sessionStorage.getItem('pwa-prompt-dismissed')
    if (dismissed) {
      setShowPrompt(false)
    }
  }, [])

  if (isStandalone || (!deferredPrompt && !isIOS)) {
    return null
  }

  return (
    <AnimatePresence>
      {showPrompt && (
        <motion.div
          initial={{ opacity: 0, y: 100 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 100 }}
          className="fixed bottom-20 md:bottom-4 left-4 right-4 z-50"
        >
          <div className="bg-white rounded-lg shadow-lg border border-secondary-200 p-4 max-w-sm mx-auto">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Smartphone className="w-5 h-5 text-primary-600" />
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="text-sm font-semibold text-secondary-900">
                  Install Grocify
                </h3>
                <p className="text-xs text-secondary-600 mt-1">
                  Add to your home screen for quick access and offline use.
                </p>
                
                <div className="flex items-center space-x-2 mt-3">
                  <motion.button
                    onClick={handleInstall}
                    className="flex items-center space-x-1 bg-primary-600 text-white px-3 py-1.5 rounded-md text-xs font-medium"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Download className="w-3 h-3" />
                    <span>Install</span>
                  </motion.button>
                  
                  <motion.button
                    onClick={handleDismiss}
                    className="text-xs text-secondary-500 hover:text-secondary-700 px-2 py-1.5"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Not now
                  </motion.button>
                </div>
              </div>
              
              <motion.button
                onClick={handleDismiss}
                className="flex-shrink-0 p-1 text-secondary-400 hover:text-secondary-600"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <X className="w-4 h-4" />
              </motion.button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
