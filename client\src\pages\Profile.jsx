import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Moon, 
  Sun, 
  Bell, 
  DollarSign,
  Download,
  Trash2,
  RefreshC<PERSON>,
  Shield,
  HelpCircle
} from 'lucide-react'
import { useAppStore, useGroceryStore, usePantryStore, usePriceStore } from '../stores/useStore'
import toast from 'react-hot-toast'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3
    }
  }
}

export default function Profile() {
  const { theme, setTheme, preferences, setPreferences, deferredPrompt } = useAppStore()
  const groceryStore = useGroceryStore()
  const pantryStore = usePantryStore()
  const priceStore = usePriceStore()

  const [showDataExport, setShowDataExport] = useState(false)

  const handleThemeToggle = () => {
    setTheme(theme === 'light' ? 'dark' : 'light')
    toast.success(`Switched to ${theme === 'light' ? 'dark' : 'light'} mode`)
  }

  const handlePreferenceChange = (key, value) => {
    setPreferences({
      ...preferences,
      [key]: value
    })
    toast.success('Preferences updated')
  }

  const handleInstallPWA = async () => {
    if (deferredPrompt) {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        toast.success('App installed successfully!')
      }
    } else {
      toast.error('Installation not available')
    }
  }

  const handleExportData = () => {
    const data = {
      groceryItems: groceryStore.items,
      pantryItems: pantryStore.items,
      prices: priceStore.prices,
      preferences: preferences,
      exportDate: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `grocify-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success('Data exported successfully')
  }

  const handleClearAllData = () => {
    if (window.confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      // Clear all stores
      groceryStore.items.forEach(item => groceryStore.deleteItem(item.id))
      pantryStore.items.forEach(item => pantryStore.deleteItem(item.id))
      // Note: Price store doesn't have a clear method, would need to add one
      
      toast.success('All data cleared')
    }
  }

  const getTotalItems = () => {
    return groceryStore.items.length + pantryStore.items.length + priceStore.prices.length
  }

  const getStorageSize = () => {
    // Estimate storage size
    const data = JSON.stringify({
      grocery: groceryStore.items,
      pantry: pantryStore.items,
      prices: priceStore.prices
    })
    return (new Blob([data]).size / 1024).toFixed(2) // KB
  }

  return (
    <motion.div 
      className="container py-8"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900 mb-2">
          Profile & Settings
        </h1>
        <p className="text-secondary-600">
          Manage your preferences and app settings
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Settings */}
        <div className="lg:col-span-2 space-y-6">
          {/* User Profile */}
          <motion.div variants={itemVariants} className="card">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
                <User className="w-8 h-8 text-primary-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-secondary-900">Guest User</h2>
                <p className="text-secondary-600">Using Grocify in guest mode</p>
              </div>
            </div>
            
            <div className="bg-primary-50 border border-primary-200 rounded-lg p-4">
              <h3 className="font-medium text-primary-900 mb-2">Guest Mode</h3>
              <p className="text-sm text-primary-700 mb-3">
                Your data is stored locally on this device. Consider creating an account to sync across devices.
              </p>
              <button className="btn-primary text-sm">
                Create Account (Coming Soon)
              </button>
            </div>
          </motion.div>

          {/* App Preferences */}
          <motion.div variants={itemVariants} className="card">
            <h2 className="text-xl font-semibold text-secondary-900 mb-6 flex items-center space-x-2">
              <Settings className="w-5 h-5" />
              <span>App Preferences</span>
            </h2>

            <div className="space-y-6">
              {/* Theme */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-secondary-900">Theme</h3>
                  <p className="text-sm text-secondary-600">Choose your preferred color scheme</p>
                </div>
                <motion.button
                  onClick={handleThemeToggle}
                  className="flex items-center space-x-2 p-2 rounded-lg bg-secondary-100 hover:bg-secondary-200 transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {theme === 'light' ? (
                    <>
                      <Moon className="w-4 h-4" />
                      <span className="text-sm">Dark</span>
                    </>
                  ) : (
                    <>
                      <Sun className="w-4 h-4" />
                      <span className="text-sm">Light</span>
                    </>
                  )}
                </motion.button>
              </div>

              {/* Currency */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-secondary-900">Currency</h3>
                  <p className="text-sm text-secondary-600">Default currency for price tracking</p>
                </div>
                <select
                  value={preferences.currency}
                  onChange={(e) => handlePreferenceChange('currency', e.target.value)}
                  className="input w-24"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="CAD">CAD</option>
                </select>
              </div>

              {/* Notifications */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-secondary-900">Notifications</h3>
                  <p className="text-sm text-secondary-600">Get alerts for expiring items and price drops</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.notifications}
                    onChange={(e) => handlePreferenceChange('notifications', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-secondary-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-secondary-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>

              {/* Auto Suggestions */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-secondary-900">Smart Suggestions</h3>
                  <p className="text-sm text-secondary-600">Show suggestions based on your history</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.autoSuggest}
                    onChange={(e) => handlePreferenceChange('autoSuggest', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-secondary-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-secondary-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>
            </div>
          </motion.div>

          {/* Data Management */}
          <motion.div variants={itemVariants} className="card">
            <h2 className="text-xl font-semibold text-secondary-900 mb-6 flex items-center space-x-2">
              <Shield className="w-5 h-5" />
              <span>Data Management</span>
            </h2>

            <div className="space-y-4">
              <motion.button
                onClick={handleExportData}
                className="w-full flex items-center justify-between p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors"
                whileHover={{ scale: 1.01 }}
                whileTap={{ scale: 0.99 }}
              >
                <div className="flex items-center space-x-3">
                  <Download className="w-5 h-5 text-primary-600" />
                  <div className="text-left">
                    <h3 className="font-medium text-secondary-900">Export Data</h3>
                    <p className="text-sm text-secondary-600">Download all your data as JSON</p>
                  </div>
                </div>
              </motion.button>

              <motion.button
                onClick={handleClearAllData}
                className="w-full flex items-center justify-between p-4 border border-danger-200 rounded-lg hover:bg-danger-50 transition-colors"
                whileHover={{ scale: 1.01 }}
                whileTap={{ scale: 0.99 }}
              >
                <div className="flex items-center space-x-3">
                  <Trash2 className="w-5 h-5 text-danger-600" />
                  <div className="text-left">
                    <h3 className="font-medium text-danger-900">Clear All Data</h3>
                    <p className="text-sm text-danger-600">Remove all items and reset the app</p>
                  </div>
                </div>
              </motion.button>
            </div>
          </motion.div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* App Info */}
          <motion.div variants={itemVariants} className="card">
            <h3 className="text-lg font-semibold text-secondary-900 mb-4">App Info</h3>
            
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-secondary-600">Version</span>
                <span className="font-medium">1.0.0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Total Items</span>
                <span className="font-medium">{getTotalItems()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Storage Used</span>
                <span className="font-medium">{getStorageSize()} KB</span>
              </div>
            </div>
          </motion.div>

          {/* PWA Install */}
          {deferredPrompt && (
            <motion.div variants={itemVariants} className="card">
              <h3 className="text-lg font-semibold text-secondary-900 mb-4">Install App</h3>
              <p className="text-sm text-secondary-600 mb-4">
                Install Grocify for quick access and offline use
              </p>
              <motion.button
                onClick={handleInstallPWA}
                className="btn-primary w-full flex items-center justify-center space-x-2"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Download className="w-4 h-4" />
                <span>Install App</span>
              </motion.button>
            </motion.div>
          )}

          {/* Help & Support */}
          <motion.div variants={itemVariants} className="card">
            <h3 className="text-lg font-semibold text-secondary-900 mb-4 flex items-center space-x-2">
              <HelpCircle className="w-5 h-5" />
              <span>Help & Support</span>
            </h3>
            
            <div className="space-y-2 text-sm">
              <button className="w-full text-left p-2 rounded hover:bg-secondary-50 text-primary-600">
                User Guide
              </button>
              <button className="w-full text-left p-2 rounded hover:bg-secondary-50 text-primary-600">
                Keyboard Shortcuts
              </button>
              <button className="w-full text-left p-2 rounded hover:bg-secondary-50 text-primary-600">
                Report a Bug
              </button>
              <button className="w-full text-left p-2 rounded hover:bg-secondary-50 text-primary-600">
                Feature Request
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  )
}
