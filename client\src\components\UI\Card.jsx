import { motion } from 'framer-motion'
import { cn } from '../../lib/utils'

const Card = ({ 
  children, 
  className, 
  variant = 'default',
  interactive = false,
  glass = false,
  ...props 
}) => {
  const variants = {
    default: 'card',
    glass: 'card card-glass',
    elevated: 'card shadow-large',
    outlined: 'card border-2'
  }

  const cardClass = cn(
    variants[variant],
    interactive && 'card-interactive cursor-pointer',
    glass && 'card-glass',
    className
  )

  if (interactive) {
    return (
      <motion.div
        className={cardClass}
        whileHover={{ 
          y: -4,
          transition: { duration: 0.2, ease: "easeOut" }
        }}
        whileTap={{ 
          scale: 0.98,
          transition: { duration: 0.1 }
        }}
        {...props}
      >
        {children}
      </motion.div>
    )
  }

  return (
    <div className={cardClass} {...props}>
      {children}
    </div>
  )
}

const CardHeader = ({ children, className, ...props }) => (
  <div className={cn('card-header', className)} {...props}>
    {children}
  </div>
)

const CardTitle = ({ children, className, ...props }) => (
  <h3 className={cn('card-title', className)} {...props}>
    {children}
  </h3>
)

const CardDescription = ({ children, className, ...props }) => (
  <p className={cn('card-description', className)} {...props}>
    {children}
  </p>
)

const CardContent = ({ children, className, ...props }) => (
  <div className={cn('card-content', className)} {...props}>
    {children}
  </div>
)

const CardFooter = ({ children, className, ...props }) => (
  <div className={cn('card-footer', className)} {...props}>
    {children}
  </div>
)

// Stats Card Component
const StatsCard = ({ 
  title, 
  value, 
  change, 
  changeType = 'positive',
  icon: Icon,
  className,
  ...props 
}) => {
  const changeColor = changeType === 'positive' ? 'text-green-600' : 'text-red-600'
  const changeBg = changeType === 'positive' ? 'bg-green-50' : 'bg-red-50'

  return (
    <Card className={cn('p-6', className)} interactive {...props}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold text-foreground mt-1">{value}</p>
          {change && (
            <div className={cn('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-2', changeBg, changeColor)}>
              {change}
            </div>
          )}
        </div>
        {Icon && (
          <div className="ml-4">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
              <Icon className="w-6 h-6 text-primary" />
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}

// Feature Card Component
const FeatureCard = ({ 
  icon: Icon, 
  title, 
  description, 
  className,
  ...props 
}) => (
  <Card className={cn('p-6 text-center', className)} interactive {...props}>
    <motion.div
      className="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4"
      whileHover={{ 
        scale: 1.1,
        rotate: 5,
        transition: { duration: 0.2 }
      }}
    >
      <Icon className="w-8 h-8 text-primary" />
    </motion.div>
    <CardTitle className="mb-2">{title}</CardTitle>
    <CardDescription>{description}</CardDescription>
  </Card>
)

Card.Header = CardHeader
Card.Title = CardTitle
Card.Description = CardDescription
Card.Content = CardContent
Card.Footer = CardFooter
Card.Stats = StatsCard
Card.Feature = FeatureCard

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, StatsCard, FeatureCard }
export default Card
