import { motion } from 'framer-motion'
import { 
  ShoppingCart, 
  TrendingUp, 
  Package, 
  Smartphone,
  Wifi,
  Bell,
  ArrowRight,
  CheckCircle
} from 'lucide-react'
import { useAppStore } from '../stores/useStore'

const features = [
  {
    icon: ShoppingCart,
    title: 'Smart Grocery Lists',
    description: 'Create and manage grocery lists with intelligent suggestions based on your shopping history.'
  },
  {
    icon: TrendingUp,
    title: 'Price Tracking',
    description: 'Track prices across different stores and get alerts when items go on sale.'
  },
  {
    icon: Package,
    title: 'Pantry Management',
    description: 'Keep track of your pantry inventory and get expiry date reminders.'
  },
  {
    icon: Wifi,
    title: 'Offline First',
    description: 'Works completely offline. Your data is always available, even without internet.'
  },
  {
    icon: Smartphone,
    title: 'Mobile Optimized',
    description: 'Designed for mobile with a beautiful, responsive interface that works on any device.'
  },
  {
    icon: Bell,
    title: 'Smart Notifications',
    description: 'Get notified about expiring items, price drops, and shopping reminders.'
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
}

export default function Landing() {
  const { setCurrentPage } = useAppStore()

  const handleStartAsGuest = () => {
    setCurrentPage('dashboard')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50">
      {/* Hero Section */}
      <motion.section 
        className="container py-20 md:py-32"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <div className="text-center max-w-4xl mx-auto">
          <motion.div
            variants={itemVariants}
            className="flex justify-center mb-8"
          >
            <div className="w-20 h-20 bg-primary-600 rounded-2xl flex items-center justify-center shadow-lg">
              <ShoppingCart className="w-10 h-10 text-white" />
            </div>
          </motion.div>

          <motion.h1 
            variants={itemVariants}
            className="text-4xl md:text-6xl font-bold text-secondary-900 mb-6 text-balance"
          >
            Smart Grocery Management
            <span className="text-primary-600"> Made Simple</span>
          </motion.h1>

          <motion.p 
            variants={itemVariants}
            className="text-xl text-secondary-600 mb-8 max-w-2xl mx-auto text-balance"
          >
            Organize your shopping, track prices, manage your pantry, and never forget an item again. 
            Works offline and syncs when you're back online.
          </motion.p>

          <motion.div 
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <motion.button
              onClick={handleStartAsGuest}
              className="btn-primary px-8 py-4 text-lg font-semibold flex items-center space-x-2 shadow-lg"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>Start as Guest</span>
              <ArrowRight className="w-5 h-5" />
            </motion.button>

            <motion.div 
              className="flex items-center space-x-2 text-sm text-secondary-500"
              whileHover={{ scale: 1.05 }}
            >
              <CheckCircle className="w-4 h-4 text-success-500" />
              <span>No signup required</span>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* Features Section */}
      <motion.section 
        className="container py-20"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
        variants={containerVariants}
      >
        <motion.div variants={itemVariants} className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 mb-4">
            Everything you need for smart shopping
          </h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            Grocify combines the best features of grocery management, price tracking, 
            and pantry organization in one beautiful, easy-to-use app.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div
                key={index}
                variants={itemVariants}
                className="card hover:shadow-md transition-shadow duration-300"
                whileHover={{ y: -5 }}
              >
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                  <Icon className="w-6 h-6 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-secondary-600">
                  {feature.description}
                </p>
              </motion.div>
            )
          })}
        </div>
      </motion.section>

      {/* CTA Section */}
      <motion.section 
        className="container py-20"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={containerVariants}
      >
        <motion.div 
          variants={itemVariants}
          className="bg-primary-600 rounded-2xl p-8 md:p-12 text-center text-white"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to get organized?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join thousands of users who have simplified their grocery shopping with Grocify.
          </p>
          
          <motion.button
            onClick={handleStartAsGuest}
            className="bg-white text-primary-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-primary-50 transition-colors flex items-center space-x-2 mx-auto"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <span>Get Started Now</span>
            <ArrowRight className="w-5 h-5" />
          </motion.button>
        </motion.div>
      </motion.section>
    </div>
  )
}
