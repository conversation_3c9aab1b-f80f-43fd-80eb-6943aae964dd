import { forwardRef, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Eye, EyeOff, AlertCircle, Check } from 'lucide-react'
import { cn } from '../../lib/utils'

const Input = forwardRef(({ 
  className, 
  type = "text",
  label,
  placeholder,
  error,
  success,
  helperText,
  leftIcon,
  rightIcon,
  floating = false,
  ...props 
}, ref) => {
  const [showPassword, setShowPassword] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const [hasValue, setHasValue] = useState(props.value || props.defaultValue || false)

  const isPassword = type === "password"
  const inputType = isPassword && showPassword ? "text" : type

  const handleChange = (e) => {
    setHasValue(e.target.value.length > 0)
    props.onChange?.(e)
  }

  if (floating) {
    return (
      <div className="relative">
        <input
          ref={ref}
          type={inputType}
          className={cn(
            "input peer",
            leftIcon && "pl-10",
            (rightIcon || isPassword) && "pr-10",
            error && "border-destructive focus-visible:ring-destructive",
            success && "border-success-500 focus-visible:ring-success-500",
            className
          )}
          placeholder=" "
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onChange={handleChange}
          {...props}
        />
        
        {label && (
          <motion.label
            className={cn(
              "absolute left-3 text-sm text-muted-foreground transition-all duration-200 pointer-events-none",
              "peer-placeholder-shown:top-3 peer-placeholder-shown:text-base peer-placeholder-shown:text-muted-foreground",
              "peer-focus:-top-2 peer-focus:left-2 peer-focus:text-xs peer-focus:text-primary peer-focus:bg-background peer-focus:px-1",
              (hasValue || isFocused) && "-top-2 left-2 text-xs text-primary bg-background px-1"
            )}
            animate={{
              y: (hasValue || isFocused) ? -8 : 0,
              scale: (hasValue || isFocused) ? 0.85 : 1,
            }}
            transition={{ duration: 0.2 }}
          >
            {label}
          </motion.label>
        )}

        {/* Left Icon */}
        {leftIcon && (
          <div className="absolute left-3 top-3 text-muted-foreground">
            {leftIcon}
          </div>
        )}

        {/* Right Icon / Password Toggle */}
        {(rightIcon || isPassword) && (
          <div className="absolute right-3 top-3">
            {isPassword ? (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            ) : (
              rightIcon
            )}
          </div>
        )}

        {/* Status Icons */}
        {error && (
          <div className="absolute right-3 top-3 text-destructive">
            <AlertCircle className="h-4 w-4" />
          </div>
        )}
        {success && (
          <div className="absolute right-3 top-3 text-success-500">
            <Check className="h-4 w-4" />
          </div>
        )}

        {/* Error/Helper Text */}
        <AnimatePresence>
          {(error || helperText) && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className={cn(
                "mt-1 text-xs",
                error ? "text-destructive" : "text-muted-foreground"
              )}
            >
              {error || helperText}
            </motion.p>
          )}
        </AnimatePresence>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
        </label>
      )}
      
      <div className="relative">
        <input
          ref={ref}
          type={inputType}
          className={cn(
            "input",
            leftIcon && "pl-10",
            (rightIcon || isPassword || error || success) && "pr-10",
            error && "border-destructive focus-visible:ring-destructive",
            success && "border-success-500 focus-visible:ring-success-500",
            className
          )}
          placeholder={placeholder}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onChange={handleChange}
          {...props}
        />

        {/* Left Icon */}
        {leftIcon && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
            {leftIcon}
          </div>
        )}

        {/* Right Icon / Password Toggle */}
        {(rightIcon || isPassword) && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            {isPassword ? (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            ) : (
              rightIcon
            )}
          </div>
        )}

        {/* Status Icons */}
        {error && !isPassword && !rightIcon && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-destructive">
            <AlertCircle className="h-4 w-4" />
          </div>
        )}
        {success && !isPassword && !rightIcon && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-success-500">
            <Check className="h-4 w-4" />
          </div>
        )}
      </div>

      {/* Error/Helper Text */}
      <AnimatePresence>
        {(error || helperText) && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className={cn(
              "text-xs",
              error ? "text-destructive" : "text-muted-foreground"
            )}
          >
            {error || helperText}
          </motion.p>
        )}
      </AnimatePresence>
    </div>
  )
})

Input.displayName = "Input"

// Textarea Component
const Textarea = forwardRef(({ 
  className, 
  label,
  error,
  helperText,
  ...props 
}, ref) => {
  return (
    <div className="space-y-2">
      {label && (
        <label className="text-sm font-medium leading-none">
          {label}
        </label>
      )}
      
      <textarea
        ref={ref}
        className={cn(
          "flex min-h-[80px] w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none transition-all duration-200",
          error && "border-destructive focus-visible:ring-destructive",
          className
        )}
        {...props}
      />

      <AnimatePresence>
        {(error || helperText) && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className={cn(
              "text-xs",
              error ? "text-destructive" : "text-muted-foreground"
            )}
          >
            {error || helperText}
          </motion.p>
        )}
      </AnimatePresence>
    </div>
  )
})

Textarea.displayName = "Textarea"

export { Input, Textarea }
