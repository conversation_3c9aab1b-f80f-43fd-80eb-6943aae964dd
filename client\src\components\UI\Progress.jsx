import { motion } from 'framer-motion'
import { cn } from '../../lib/utils'

const Progress = ({ 
  value = 0, 
  max = 100,
  className,
  variant = 'default',
  size = 'md',
  showValue = false,
  animated = true,
  striped = false,
  ...props 
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)

  const variants = {
    default: 'bg-primary',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
    info: 'bg-blue-500'
  }

  const sizes = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
    xl: 'h-4'
  }

  return (
    <div className="w-full">
      {showValue && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-foreground">Progress</span>
          <span className="text-sm text-muted-foreground">{Math.round(percentage)}%</span>
        </div>
      )}
      
      <div 
        className={cn(
          'progress',
          sizes[size],
          striped && 'progress-striped',
          className
        )}
        {...props}
      >
        <motion.div
          className={cn(
            'progress-indicator',
            variants[variant]
          )}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={animated ? { 
            duration: 0.5, 
            ease: "easeOut" 
          } : { duration: 0 }}
        />
      </div>
    </div>
  )
}

// Circular Progress
const CircularProgress = ({ 
  value = 0, 
  max = 100,
  size = 80,
  strokeWidth = 8,
  className,
  variant = 'default',
  showValue = true,
  ...props 
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (percentage / 100) * circumference

  const colors = {
    default: 'stroke-primary',
    success: 'stroke-green-500',
    warning: 'stroke-yellow-500',
    error: 'stroke-red-500',
    info: 'stroke-blue-500'
  }

  return (
    <div className={cn('relative inline-flex items-center justify-center', className)} {...props}>
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-muted/20"
        />
        
        {/* Progress circle */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeLinecap="round"
          className={colors[variant]}
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          style={{
            strokeDasharray,
          }}
        />
      </svg>
      
      {showValue && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-sm font-medium text-foreground">
            {Math.round(percentage)}%
          </span>
        </div>
      )}
    </div>
  )
}

// Step Progress
const StepProgress = ({ 
  steps = [], 
  currentStep = 0, 
  className,
  variant = 'default',
  ...props 
}) => {
  const colors = {
    default: 'bg-primary border-primary text-primary-foreground',
    success: 'bg-green-500 border-green-500 text-white',
    warning: 'bg-yellow-500 border-yellow-500 text-white',
    error: 'bg-red-500 border-red-500 text-white',
    info: 'bg-blue-500 border-blue-500 text-white'
  }

  return (
    <div className={cn('flex items-center', className)} {...props}>
      {steps.map((step, index) => (
        <div key={index} className="flex items-center">
          {/* Step circle */}
          <motion.div
            className={cn(
              'w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-medium transition-all duration-200',
              index <= currentStep 
                ? colors[variant]
                : 'bg-background border-muted text-muted-foreground'
            )}
            initial={{ scale: 0.8 }}
            animate={{ scale: index === currentStep ? 1.1 : 1 }}
            transition={{ duration: 0.2 }}
          >
            {index + 1}
          </motion.div>
          
          {/* Step label */}
          {step.label && (
            <span className={cn(
              'ml-2 text-sm font-medium',
              index <= currentStep ? 'text-foreground' : 'text-muted-foreground'
            )}>
              {step.label}
            </span>
          )}
          
          {/* Connector line */}
          {index < steps.length - 1 && (
            <div className="flex-1 mx-4">
              <div className="h-0.5 bg-muted relative overflow-hidden">
                <motion.div
                  className={cn('h-full', colors[variant].split(' ')[0])}
                  initial={{ width: '0%' }}
                  animate={{ 
                    width: index < currentStep ? '100%' : '0%' 
                  }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                />
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

// Loading Progress (indeterminate)
const LoadingProgress = ({ 
  className,
  variant = 'default',
  size = 'md',
  ...props 
}) => {
  const variants = {
    default: 'bg-primary',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
    info: 'bg-blue-500'
  }

  const sizes = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
    xl: 'h-4'
  }

  return (
    <div 
      className={cn(
        'progress overflow-hidden',
        sizes[size],
        className
      )}
      {...props}
    >
      <motion.div
        className={cn(
          'h-full w-1/3 rounded-full',
          variants[variant]
        )}
        animate={{
          x: ['-100%', '400%']
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  )
}

Progress.Circular = CircularProgress
Progress.Step = StepProgress
Progress.Loading = LoadingProgress

export { Progress, CircularProgress, StepProgress, LoadingProgress }
export default Progress
